<?php
session_start();
require '../includes/db_connect.php';
require '../includes/auth_check.php';

// Mark as read when viewed
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $pdo->prepare("
        UPDATE notifications 
        SET is_read = TRUE 
        WHERE id = ? AND user_id = ?
    ")->execute([$_POST['notif_id'], $_SESSION['user_id']]);
    exit;
}

// Get user notifications
$notifications = $pdo->prepare("
    SELECT * FROM notifications 
    WHERE user_id = ? 
    ORDER BY created_at DESC
");
$notifications->execute([$_SESSION['user_id']]);
?>
<!DOCTYPE html>
<html>
<head>
    <title>My Notifications</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Notifications</h2>
        
        <div class="list-group mt-3">
            <?php while($notif = $notifications->fetch()): ?>
            <div class="list-group-item <?= $notif['is_read'] ? '' : 'list-group-item-primary' ?>">
                <div class="d-flex justify-content-between">
                    <p class="mb-1"><?= htmlspecialchars($notif['message']) ?></p>
                    <small><?= date('M j, g:i a', strtotime($notif['created_at'])) ?></small>
                </div>
                <?php if(!$notif['is_read']): ?>
                <form method="POST" class="mark-read-form">
                    <input type="hidden" name="notif_id" value="<?= $notif['id'] ?>">
                    <button type="submit" class="btn btn-sm btn-outline-secondary">Mark Read</button>
                </form>
                <?php endif; ?>
            </div>
            <?php endwhile; ?>
        </div>
    </div>

    <script>
        // AJAX mark as read
        document.querySelectorAll('.mark-read-form').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                fetch('', {
                    method: 'POST',
                    body: new FormData(this)
                }).then(() => {
                    this.parentElement.classList.remove('list-group-item-primary');
                    this.remove();
                });
            });
        });
    </script>
</body>
</html>