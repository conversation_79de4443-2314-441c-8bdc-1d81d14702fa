<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require 'includes/db_connect.php';
require 'includes/auth_check.php';
require 'includes/functions.php';
require 'config.php';

// Get event ID
$eventId = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$eventId) {
    header('Location: events.php');
    exit;
}

// Get event details
$stmt = $pdo->prepare("
    SELECT e.*, u.username, u.role as user_role, ea.views, ea.shares,
           (SELECT COUNT(*) FROM event_images ei WHERE ei.event_id = e.id) as image_count
    FROM events e
    LEFT JOIN users u ON e.user_id = u.id
    LEFT JOIN event_analytics ea ON e.id = ea.event_id
    WHERE e.id = ?
");
$stmt->execute([$eventId]);
$event = $stmt->fetch();

if (!$event) {
    header('Location: events.php');
    exit;
}

// Check if user can view this event
if (!$event['is_approved'] && $_SESSION['user_id'] != $event['user_id'] && $_SESSION['role'] !== 'admin') {
    header('Location: events.php');
    exit;
}

// Get event images
$imageStmt = $pdo->prepare("SELECT * FROM event_images WHERE event_id = ? ORDER BY is_primary DESC, uploaded_at ASC");
$imageStmt->execute([$eventId]);
$images = $imageStmt->fetchAll();

// Update view count
$updateViews = $pdo->prepare("
    INSERT INTO event_analytics (event_id, views) VALUES (?, 1)
    ON DUPLICATE KEY UPDATE views = views + 1, last_viewed = NOW()
");
$updateViews->execute([$eventId]);

// Get event type configuration
$eventConfig = $EVENT_TYPES[$event['type']] ?? $EVENT_TYPES['accident'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($event['title']) ?> | <?= APP_NAME ?></title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/leaflet@1.9.4/dist/leaflet.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 5px 20px rgba(0, 0, 0, 0.15);
            --border-radius: 15px;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            box-shadow: var(--shadow-light);
        }

        .navbar-brand {
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .main-content {
            padding: 2rem 0;
        }

        .event-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .event-header {
            background: var(--gradient-primary);
            color: white;
            padding: 2rem;
        }

        .event-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .event-meta {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
            opacity: 0.9;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .event-body {
            padding: 2rem;
        }

        .status-badges {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-approved {
            background: rgba(39, 174, 96, 0.1);
            color: var(--success-color);
            border: 1px solid var(--success-color);
        }

        .status-pending {
            background: rgba(243, 156, 18, 0.1);
            color: var(--warning-color);
            border: 1px solid var(--warning-color);
        }

        .type-badge {
            background: rgba(52, 152, 219, 0.1);
            color: var(--secondary-color);
            border: 1px solid var(--secondary-color);
        }

        .severity-low { background: rgba(39, 174, 96, 0.1); color: var(--success-color); border: 1px solid var(--success-color); }
        .severity-medium { background: rgba(52, 152, 219, 0.1); color: var(--secondary-color); border: 1px solid var(--secondary-color); }
        .severity-high { background: rgba(243, 156, 18, 0.1); color: var(--warning-color); border: 1px solid var(--warning-color); }
        .severity-critical { background: rgba(231, 76, 60, 0.1); color: var(--danger-color); border: 1px solid var(--danger-color); }

        .event-description {
            font-size: 1.1rem;
            line-height: 1.6;
            color: #2c3e50;
            margin-bottom: 2rem;
        }

        .event-images {
            margin-bottom: 2rem;
        }

        .image-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .image-item {
            border-radius: 12px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .image-item:hover {
            transform: scale(1.05);
        }

        .image-item img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .location-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        #map {
            height: 300px;
            border-radius: 12px;
            margin-top: 1rem;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        .btn-custom {
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-light);
        }

        .back-btn {
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #5a6268;
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 1rem 0;
            }

            .event-header {
                padding: 1.5rem;
            }

            .event-title {
                font-size: 1.5rem;
            }

            .event-meta {
                flex-direction: column;
                gap: 1rem;
            }

            .event-body {
                padding: 1.5rem;
            }

            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-shield-alt me-2"></i>
                DOWN-RWANDA
            </a>

            <div class="d-flex gap-2">
                <a href="events.php" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-list me-1"></i>
                    All Events
                </a>
                <a href="my_events.php" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-user-circle me-1"></i>
                    My Events
                </a>
                <a href="index.php" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-home me-1"></i>
                    Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container main-content">
        <a href="javascript:history.back()" class="back-btn">
            <i class="fas fa-arrow-left"></i>
            Back
        </a>

        <div class="event-card">
            <div class="event-header">
                <div class="event-title"><?= htmlspecialchars($event['title']) ?></div>
                <div class="event-meta">
                    <div class="meta-item">
                        <i class="fas fa-user"></i>
                        <span>Reported by <?= htmlspecialchars($event['username']) ?></span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-calendar"></i>
                        <span><?= formatTimeAgo($event['created_at']) ?></span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-eye"></i>
                        <span><?= number_format($event['views'] ?? 0) ?> views</span>
                    </div>
                    <?php if ($event['image_count']): ?>
                    <div class="meta-item">
                        <i class="fas fa-images"></i>
                        <span><?= $event['image_count'] ?> images</span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="event-body">
                <div class="status-badges">
                    <span class="status-badge status-<?= $event['is_approved'] ? 'approved' : 'pending' ?>">
                        <?= $event['is_approved'] ? 'Approved' : 'Pending Approval' ?>
                    </span>
                    <span class="status-badge type-badge">
                        <i class="fas fa-<?= $eventConfig['icon'] ?> me-1"></i>
                        <?= $eventConfig['name'] ?>
                    </span>
                    <span class="status-badge severity-<?= $event['severity_level'] ?>">
                        <?= ucfirst($event['severity_level']) ?> Priority
                    </span>
                </div>

                <?php if ($event['description']): ?>
                <div class="event-description">
                    <?= nl2br(htmlspecialchars($event['description'])) ?>
                </div>
                <?php endif; ?>

                <?php if (!empty($images)): ?>
                <div class="event-images">
                    <h5><i class="fas fa-images me-2"></i>Event Images</h5>
                    <div class="image-gallery">
                        <?php foreach ($images as $image): ?>
                        <div class="image-item" onclick="viewImage('<?= htmlspecialchars($image['image_path']) ?>')">
                            <img src="<?= htmlspecialchars($image['image_path']) ?>" alt="Event Image" loading="lazy">
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <div class="location-section">
                    <h5><i class="fas fa-map-marker-alt me-2"></i>Location</h5>
                    <?php if ($event['location_description']): ?>
                    <p class="mb-2"><?= htmlspecialchars($event['location_description']) ?></p>
                    <?php endif; ?>
                    <p class="text-muted mb-0">
                        <small>Coordinates: <?= $event['latitude'] ?>, <?= $event['longitude'] ?></small>
                    </p>
                    <div id="map"></div>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary btn-custom" onclick="shareEvent()">
                        <i class="fas fa-share me-2"></i>Share Event
                    </button>
                    <button class="btn btn-outline-secondary btn-custom" onclick="viewOnMap()">
                        <i class="fas fa-map me-2"></i>View on Full Map
                    </button>
                    <?php if ($_SESSION['user_id'] == $event['user_id'] && !$event['is_approved']): ?>
                    <a href="edit_event.php?id=<?= $event['id'] ?>" class="btn btn-outline-warning btn-custom">
                        <i class="fas fa-edit me-2"></i>Edit Event
                    </a>
                    <?php endif; ?>
                    <?php if ($_SESSION['role'] === 'admin'): ?>
                    <button class="btn btn-outline-success btn-custom" onclick="approveEvent()">
                        <i class="fas fa-check me-2"></i>Approve Event
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Initialize map
        const map = L.map('map').setView([<?= $event['latitude'] ?>, <?= $event['longitude'] ?>], 15);

        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors',
            maxZoom: 18
        }).addTo(map);

        // Add event marker
        const eventIcon = L.divIcon({
            html: '<div style="background-color: <?= $eventConfig['color'] ?>; width: 30px; height: 30px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3); display: flex; align-items: center; justify-content: center; color: white; font-size: 14px;"><i class="fas fa-<?= $eventConfig['icon'] ?>"></i></div>',
            className: 'custom-marker',
            iconSize: [30, 30],
            iconAnchor: [15, 15]
        });

        L.marker([<?= $event['latitude'] ?>, <?= $event['longitude'] ?>], { icon: eventIcon })
            .addTo(map)
            .bindPopup(`
                <div style="text-align: center;">
                    <strong><?= htmlspecialchars($event['title']) ?></strong><br>
                    <small><?= htmlspecialchars($event['location_description'] ?: 'Event location') ?></small>
                </div>
            `).openPopup();

        function shareEvent() {
            const url = window.location.href;
            const title = '<?= htmlspecialchars($event['title']) ?>';

            if (navigator.share) {
                navigator.share({
                    title: title,
                    text: 'Check out this event report from DOWN-RWANDA',
                    url: url
                });
            } else {
                navigator.clipboard.writeText(url).then(() => {
                    alert('Event link copied to clipboard!');
                });
            }
        }

        function viewOnMap() {
            window.open(`map.php?lat=<?= $event['latitude'] ?>&lng=<?= $event['longitude'] ?>&zoom=16`, '_blank');
        }

        function viewImage(imagePath) {
            window.open(imagePath, '_blank');
        }

        function approveEvent() {
            if (confirm('Are you sure you want to approve this event?')) {
                // Add approval logic here
                alert('Event approval functionality would be implemented here');
            }
        }
    </script>
</body>
</html>
