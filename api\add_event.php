<?php
session_start();
require '../includes/db_connect.php';
require '../includes/auth_check.php';

// Only allow organizations to post non-accident events
if ($_SESSION['role'] !== 'organization' && $_POST['type'] !== 'accident') {
    header("Location: index.php?error=org_only");
    exit;
}

// Handle file uploads
function uploadFile($file, $directory) {
    if (!is_dir($directory)) mkdir($directory, 0755, true);
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid().'.'.$extension;
    move_uploaded_file($file['tmp_name'], $directory.$filename);
    return $directory.$filename;
}

// Process event
try {
    $isPaid = ($_POST['type'] !== 'accident' && $_SESSION['role'] === 'organization');
    $paymentProof = $isPaid ? uploadFile($_FILES['payment_proof'], 'uploads/payments/') : null;
    
    $stmt = $pdo->prepare("
        INSERT INTO events (
            title, type, latitude, longitude, 
            is_paid, payment_proof, is_approved, user_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([
        $_POST['title'],
        $_POST['type'],
        $_POST['latitude'],
        $_POST['longitude'],
        $isPaid,
        $paymentProof,
        $_POST['type'] === 'accident', // Auto-approve accidents
        $_SESSION['user_id']
    ]);
    
    // Send police alerts for accidents
    if ($_POST['type'] === 'accident') {
        require '../includes/send_police_alerts.php';
        sendPoliceAlert($_POST['latitude'], $_POST['longitude']);
    }
    
    header("Location: index.php?success=1");
} catch (PDOException $e) {
    header("Location: index.php?error=database");
}
?>