<?php
session_start();
require 'includes/db_connect.php';
require 'includes/auth_check.php';

if ($_SESSION['role'] !== 'police') {
    header("HTTP/1.1 403 Forbidden");
    exit;
}

// Get recent accidents
$accidents = $pdo->query("
    SELECT * FROM events 
    WHERE type = 'accident'
    ORDER BY created_at DESC
    LIMIT 10
")->fetchAll();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Police Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        #policeMap { height: 400px; }
        .urgent { border-left: 3px solid red; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <span class="navbar-brand">Police Portal</span>
            <div class="d-flex">
                <a href="logout.php" class="btn btn-sm btn-danger">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h2>Recent Accident Reports</h2>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Accident Map</h5>
                    </div>
                    <div class="card-body">
                        <div id="policeMap"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Latest Reports</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group">
                            <?php foreach ($accidents as $accident): ?>
                            <div class="list-group-item <?= time() - strtotime($accident['created_at']) < 3600 ? 'urgent' : '' ?>">
                                <div class="d-flex justify-content-between">
                                    <h6 class="mb-1"><?= htmlspecialchars($accident['title']) ?></h6>
                                    <small><?= date('H:i', strtotime($accident['created_at'])) ?></small>
                                </div>
                                <p class="mb-1">Location: <?= $accident['latitude'] ?>, <?= $accident['longitude'] ?></p>
                                <small>Reported <?= time_elapsed_string($accident['created_at']) ?></small>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Initialize police map
        const policeMap = L.map('policeMap').setView([-1.9403, 29.8739], 12);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(policeMap);

        // Add accident markers
        <?php foreach ($accidents as $accident): ?>
            L.marker([<?= $accident['latitude'] ?>, <?= $accident['longitude'] ?>], {
                icon: L.divIcon({
                    html: '<i class="fa fa-ambulance"></i>',
                    className: 'accident-marker',
                    iconSize: [30, 30]
                })
            })
            .addTo(policeMap)
            .bindPopup(`
                <h6><?= addslashes($accident['title']) ?></h6>
                <p>Reported: <?= date('M j, H:i', strtotime($accident['created_at'])) ?></p>
            `);
        <?php endforeach; ?>
    </script>
</body>
</html>
<?php
function time_elapsed_string($datetime) {
    $now = new DateTime;
    $ago = new DateTime($datetime);
    $diff = $now->diff($ago);

    if ($diff->d > 0) {
        return $diff->d . ' days ago';
    } elseif ($diff->h > 0) {
        return $diff->h . ' hours ago';
    } else {
        return $diff->i . ' minutes ago';
    }
}
?>