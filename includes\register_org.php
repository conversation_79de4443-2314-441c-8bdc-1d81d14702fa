<?php
session_start();
require 'db_connect.php';

// Organization codes
$org_codes = [
    'POLICE' => 'police',
    'MEDIA' => 'organization',
    'EVENTS' => 'organization'
];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username']);
    $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
    $org_code = strtoupper(trim($_POST['org_code']));
    $phone = preg_replace('/[^0-9]/', '', $_POST['phone']);

    // Validate org code
    if (!array_key_exists($org_code, $org_codes)) {
        header("Location: ../register.php?error=invalid_code");
        exit;
    }

    try {
        $stmt = $pdo->prepare("
            INSERT INTO users (username, password, role, phone) 
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([
            $username,
            $password,
            $org_codes[$org_code],
            $phone
        ]);
        
        $_SESSION['user_id'] = $pdo->lastInsertId();
        $_SESSION['role'] = $org_codes[$org_code];
        header("Location: ../index.php");
    } catch (PDOException $e) {
        header("Location: ../register.php?error=exists");
    }
    exit;
}
?>