<?php
/**
 * <PERSON><PERSON>t to fix session_start() issues across all PHP files
 */

$files_to_fix = [
    'mobile/report.php',
    'mobile/map.php', 
    'mobile/notifications.php',
    'dashboard/advanced.php',
    'test_system.php'
];

foreach ($files_to_fix as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        
        // Replace session_start() with conditional session start
        $content = preg_replace(
            '/^<\?php\s*\n?session_start\(\);/m',
            "<?php\nif (session_status() === PHP_SESSION_NONE) {\n    session_start();\n}",
            $content
        );
        
        file_put_contents($file, $content);
        echo "Fixed: $file\n";
    } else {
        echo "File not found: $file\n";
    }
}

echo "Session fixes completed!\n";
?>
