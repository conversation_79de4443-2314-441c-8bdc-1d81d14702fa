<?php
require '../includes/db_connect.php';

// 1. Sync from Kigali Today
$kigaliToday = json_decode(file_get_contents('https://api.kigalitoday.com/events'), true);
foreach ($kigaliToday['events'] as $event) {
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO external_events 
        (source, external_id, title, description, latitude, longitude, event_date)
        VALUES ('kigali_today', ?, ?, ?, ?, ?, ?)
    ");
    $stmt->execute([
        $event['id'],
        $event['title'],
        $event['description'],
        $event['location']['lat'],
        $event['location']['lng'],
        $event['date']
    ]);
}

// 2. Sync from IGHE.com
$igheEvents = json_decode(file_get_contents('https://ighe.com/api/events'), true);
foreach ($igheEvents as $event) {
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO external_events 
        (source, external_id, title, description, latitude, longitude, event_date)
        VALUES ('ighe_com', ?, ?, ?, ?, ?, ?)
    ");
    $stmt->execute([
        $event['event_id'],
        $event['name'],
        $event['details'],
        $event['geo']['latitude'],
        $event['geo']['longitude'],
        $event['start_time']
    ]);
}

file_put_contents('logs/sync.log', date('Y-m-d H:i:s')." Synced events\n", FILE_APPEND);
?>