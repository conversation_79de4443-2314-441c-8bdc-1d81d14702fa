<?php

function handleEmergency($method, $user) {
    switch ($method) {
        case 'GET':
            getEmergencyStatus($user);
            break;
        case 'POST':
            handleEmergencyAction($user);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
}

function getEmergencyStatus($user) {
    global $pdo;
    
    $action = $_GET['action'] ?? 'status';
    
    switch ($action) {
        case 'status':
            getSystemStatus($user);
            break;
        case 'units':
            getEmergencyUnits($user);
            break;
        case 'responses':
            getActiveResponses($user);
            break;
        case 'alerts':
            getActiveAlerts($user);
            break;
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
}

function getSystemStatus($user) {
    global $pdo;
    
    try {
        // Current emergency status
        $emergencyStats = $pdo->query("
            SELECT 
                COUNT(*) as total_active_emergencies,
                COUNT(CASE WHEN severity_level = 'critical' THEN 1 END) as critical_emergencies,
                COUNT(CASE WHEN type = 'accident' THEN 1 END) as active_accidents,
                COUNT(CASE WHEN type = 'fire' THEN 1 END) as active_fires,
                COUNT(CASE WHEN type = 'medical' THEN 1 END) as active_medical
            FROM events
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            AND is_approved = TRUE
            AND type IN ('accident', 'fire', 'medical', 'crime')
        ")->fetch();
        
        // Available emergency units
        $unitStats = $pdo->query("
            SELECT 
                COUNT(*) as total_units,
                COUNT(CASE WHEN status = 'available' THEN 1 END) as available_units,
                COUNT(CASE WHEN status = 'busy' THEN 1 END) as busy_units,
                COUNT(CASE WHEN status = 'offline' THEN 1 END) as offline_units
            FROM police_units
        ")->fetch();
        
        // Recent responses
        $recentResponses = $pdo->query("
            SELECT 
                AVG(response_time) as avg_response_time,
                COUNT(*) as total_responses_24h
            FROM emergency_responses
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ")->fetch();
        
        echo json_encode([
            'success' => true,
            'data' => [
                'emergency_stats' => $emergencyStats,
                'unit_stats' => $unitStats,
                'response_stats' => $recentResponses,
                'system_status' => 'operational',
                'last_updated' => date('c')
            ]
        ]);
        
    } catch (PDOException $e) {
        logError("Failed to get emergency status: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to retrieve emergency status']);
    }
}

function getEmergencyUnits($user) {
    global $pdo;
    
    // Only emergency services can view unit details
    if (!in_array($user['role'], ['admin', 'police', 'medical', 'fire_dept'])) {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        return;
    }
    
    try {
        $units = $pdo->query("
            SELECT 
                pu.id,
                pu.unit_name,
                pu.status,
                pu.last_updated,
                u.username as officer_name,
                u.phone as officer_phone,
                ST_X(pu.current_location) as longitude,
                ST_Y(pu.current_location) as latitude
            FROM police_units pu
            LEFT JOIN users u ON pu.officer_id = u.id
            ORDER BY pu.status ASC, pu.last_updated DESC
        ")->fetchAll();
        
        echo json_encode([
            'success' => true,
            'data' => $units
        ]);
        
    } catch (PDOException $e) {
        logError("Failed to get emergency units: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to retrieve emergency units']);
    }
}

function getActiveResponses($user) {
    global $pdo;
    
    // Only emergency services can view active responses
    if (!in_array($user['role'], ['admin', 'police', 'medical', 'fire_dept'])) {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        return;
    }
    
    try {
        $responses = $pdo->query("
            SELECT 
                er.id,
                er.status,
                er.response_time,
                er.created_at,
                e.title as event_title,
                e.type as event_type,
                e.latitude,
                e.longitude,
                e.severity_level,
                pu.unit_name,
                u.username as officer_name
            FROM emergency_responses er
            JOIN events e ON er.event_id = e.id
            LEFT JOIN police_units pu ON er.police_unit_id = pu.id
            LEFT JOIN users u ON pu.officer_id = u.id
            WHERE er.status IN ('dispatched', 'arrived')
            ORDER BY er.created_at DESC
        ")->fetchAll();
        
        echo json_encode([
            'success' => true,
            'data' => $responses
        ]);
        
    } catch (PDOException $e) {
        logError("Failed to get active responses: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to retrieve active responses']);
    }
}

function getActiveAlerts($user) {
    global $pdo;
    
    try {
        // Get critical events from last 2 hours
        $alerts = $pdo->query("
            SELECT 
                e.id,
                e.title,
                e.type,
                e.severity_level,
                e.latitude,
                e.longitude,
                e.created_at,
                u.username as reporter,
                COUNT(er.id) as response_count
            FROM events e
            LEFT JOIN users u ON e.user_id = u.id
            LEFT JOIN emergency_responses er ON e.id = er.event_id
            WHERE e.severity_level = 'critical'
            AND e.created_at >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
            AND e.is_approved = TRUE
            GROUP BY e.id
            ORDER BY e.created_at DESC
        ")->fetchAll();
        
        echo json_encode([
            'success' => true,
            'data' => $alerts
        ]);
        
    } catch (PDOException $e) {
        logError("Failed to get active alerts: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to retrieve active alerts']);
    }
}

function handleEmergencyAction($user) {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'dispatch':
            dispatchUnit($input, $user);
            break;
        case 'update_response':
            updateResponse($input, $user);
            break;
        case 'update_unit_status':
            updateUnitStatus($input, $user);
            break;
        case 'send_alert':
            sendEmergencyAlert($input, $user);
            break;
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
}

function dispatchUnit($input, $user) {
    global $pdo;
    
    // Only emergency services can dispatch units
    if (!in_array($user['role'], ['admin', 'police', 'medical', 'fire_dept'])) {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        return;
    }
    
    $eventId = intval($input['event_id'] ?? 0);
    $unitId = intval($input['unit_id'] ?? 0);
    
    if (!$eventId || !$unitId) {
        http_response_code(400);
        echo json_encode(['error' => 'Event ID and Unit ID required']);
        return;
    }
    
    try {
        $pdo->beginTransaction();
        
        // Check if event exists and unit is available
        $eventCheck = $pdo->prepare("SELECT id, latitude, longitude FROM events WHERE id = ?");
        $eventCheck->execute([$eventId]);
        $event = $eventCheck->fetch();
        
        $unitCheck = $pdo->prepare("SELECT id, status, officer_id FROM police_units WHERE id = ?");
        $unitCheck->execute([$unitId]);
        $unit = $unitCheck->fetch();
        
        if (!$event || !$unit) {
            http_response_code(404);
            echo json_encode(['error' => 'Event or unit not found']);
            return;
        }
        
        if ($unit['status'] !== 'available') {
            http_response_code(400);
            echo json_encode(['error' => 'Unit is not available']);
            return;
        }
        
        // Create emergency response
        $responseStmt = $pdo->prepare("
            INSERT INTO emergency_responses (event_id, police_unit_id, status) 
            VALUES (?, ?, 'dispatched')
        ");
        $responseStmt->execute([$eventId, $unitId]);
        
        // Update unit status
        $pdo->prepare("UPDATE police_units SET status = 'busy' WHERE id = ?")
            ->execute([$unitId]);
        
        // Send notification to officer
        if ($unit['officer_id']) {
            sendNotification(
                $unit['officer_id'],
                "You have been dispatched to an emergency at {$event['latitude']}, {$event['longitude']}",
                'danger'
            );
        }
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Unit dispatched successfully',
            'response_id' => $pdo->lastInsertId()
        ]);
        
        logActivity("Emergency unit dispatched", 'INFO', [
            'event_id' => $eventId,
            'unit_id' => $unitId,
            'dispatcher_id' => $user['user_id']
        ]);
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        logError("Failed to dispatch unit: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to dispatch unit']);
    }
}

function updateResponse($input, $user) {
    global $pdo;
    
    $responseId = intval($input['response_id'] ?? 0);
    $status = sanitizeInput($input['status'] ?? '');
    $notes = sanitizeInput($input['notes'] ?? '');
    
    if (!$responseId || !$status) {
        http_response_code(400);
        echo json_encode(['error' => 'Response ID and status required']);
        return;
    }
    
    try {
        $updateFields = ["status = ?"];
        $params = [$status];
        
        if ($notes) {
            $updateFields[] = "notes = ?";
            $params[] = $notes;
        }
        
        if ($status === 'completed') {
            $updateFields[] = "response_time = TIMESTAMPDIFF(MINUTE, created_at, NOW())";
        }
        
        $params[] = $responseId;
        
        $sql = "UPDATE emergency_responses SET " . implode(", ", $updateFields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        // If completed, make unit available again
        if ($status === 'completed') {
            $pdo->query("
                UPDATE police_units pu
                JOIN emergency_responses er ON pu.id = er.police_unit_id
                SET pu.status = 'available'
                WHERE er.id = $responseId
            ");
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Response updated successfully'
        ]);
        
        logActivity("Emergency response updated", 'INFO', [
            'response_id' => $responseId,
            'status' => $status,
            'user_id' => $user['user_id']
        ]);
        
    } catch (PDOException $e) {
        logError("Failed to update response: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update response']);
    }
}

function updateUnitStatus($input, $user) {
    global $pdo;
    
    // Only emergency services can update unit status
    if (!in_array($user['role'], ['admin', 'police', 'medical', 'fire_dept'])) {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        return;
    }
    
    $unitId = intval($input['unit_id'] ?? 0);
    $status = sanitizeInput($input['status'] ?? '');
    $latitude = floatval($input['latitude'] ?? 0);
    $longitude = floatval($input['longitude'] ?? 0);
    
    if (!$unitId || !$status) {
        http_response_code(400);
        echo json_encode(['error' => 'Unit ID and status required']);
        return;
    }
    
    try {
        $updateFields = ["status = ?"];
        $params = [$status];
        
        if ($latitude && $longitude && validateCoordinates($latitude, $longitude)) {
            $updateFields[] = "current_location = POINT(?, ?)";
            $params[] = $longitude;
            $params[] = $latitude;
        }
        
        $params[] = $unitId;
        
        $sql = "UPDATE police_units SET " . implode(", ", $updateFields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        echo json_encode([
            'success' => true,
            'message' => 'Unit status updated successfully'
        ]);
        
        logActivity("Emergency unit status updated", 'INFO', [
            'unit_id' => $unitId,
            'status' => $status,
            'user_id' => $user['user_id']
        ]);
        
    } catch (PDOException $e) {
        logError("Failed to update unit status: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update unit status']);
    }
}

function sendEmergencyAlert($input, $user) {
    global $pdo;
    
    // Only emergency services can send alerts
    if (!in_array($user['role'], ['admin', 'police', 'medical', 'fire_dept'])) {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        return;
    }
    
    $message = sanitizeInput($input['message'] ?? '');
    $alertType = sanitizeInput($input['alert_type'] ?? 'warning');
    $targetArea = $input['target_area'] ?? null;
    
    if (!$message) {
        http_response_code(400);
        echo json_encode(['error' => 'Message required']);
        return;
    }
    
    try {
        $recipients = [];
        
        if ($targetArea && isset($targetArea['latitude']) && isset($targetArea['longitude']) && isset($targetArea['radius'])) {
            // Send to users in specific area
            $lat = $targetArea['latitude'];
            $lng = $targetArea['longitude'];
            $radius = $targetArea['radius'] * 1000; // Convert km to meters
            
            $stmt = $pdo->prepare("
                SELECT id FROM users 
                WHERE last_location IS NOT NULL
                AND ST_Distance_Sphere(POINT(?, ?), last_location) <= ?
            ");
            $stmt->execute([$lng, $lat, $radius]);
            $recipients = $stmt->fetchAll(PDO::FETCH_COLUMN);
        } else {
            // Send to all users
            $recipients = $pdo->query("SELECT id FROM users WHERE role != 'admin'")->fetchAll(PDO::FETCH_COLUMN);
        }
        
        if (!empty($recipients)) {
            sendBulkNotification($recipients, $message, $alertType);
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Emergency alert sent successfully',
            'recipients' => count($recipients)
        ]);
        
        logActivity("Emergency alert sent", 'WARNING', [
            'message' => $message,
            'recipients' => count($recipients),
            'sender_id' => $user['user_id']
        ]);
        
    } catch (PDOException $e) {
        logError("Failed to send emergency alert: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to send emergency alert']);
    }
}
?>
