<?php
session_start();
require 'config.php';

require 'includes/db_connect.php';
require 'includes/functions.php';

// Redirect if already logged in
if (isset($_SESSION['user_id'])) {
    header("Location: index.php");
    exit;
}

// Initialize variables
$error = '';
$username = '';
$csrf_token = bin2hex(random_bytes(32));

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        $error = "Invalid form submission";
        logActivity("CSRF token mismatch from IP: " . $_SERVER['REMOTE_ADDR']);
    } else {
        // Sanitize inputs
        $username = sanitizeInput($_POST['username']);
        $password = $_POST['password'];
        $remember = isset($_POST['remember']);

        // Input validation
        if (empty($username) || empty($password)) {
            $error = "Username and password are required";
        } else {
            try {
                // Get user from database
                $stmt = $pdo->prepare("
                    SELECT id, username, password, role, is_active 
                    FROM users 
                    WHERE username = ? 
                    LIMIT 1
                ");
                $stmt->execute([$username]);
                $user = $stmt->fetch();

                // Verify credentials
                if ($user && password_verify($password, $user['password'])) {
                    if (!$user['is_active']) {
                        $error = "Account is disabled. Contact support.";
                    } else {
                        // Regenerate session ID to prevent fixation
                        session_regenerate_id(true);

                        // Set session variables
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['username'] = $user['username'];
                        $_SESSION['role'] = $user['role'];
                        $_SESSION['last_activity'] = time();

                        // Remember me cookie
                        if ($remember) {
                            $token = bin2hex(random_bytes(32));
                            $expiry = time() + 60 * 60 * 24 * 30; // 30 days
                            setcookie('remember_token', $token, $expiry, '/', '', true, true);
                            
                            $pdo->prepare("
                                INSERT INTO auth_tokens (user_id, token, expires_at) 
                                VALUES (?, ?, ?)
                            ")->execute([$user['id'], $token, date('Y-m-d H:i:s', $expiry)]);
                        }

                        logActivity("Successful login: " . $username);
                        header("Location: index.php");
                        exit;
                    }
                } else {
                    // Generic error to prevent user enumeration
                    $error = "Invalid username or password";
                    // Delay to prevent brute force
                    sleep(1);
                }
            } catch (PDOException $e) {
                $error = "Database error. Please try again.";
                logActivity("Login error: " . $e->getMessage());
            }
        }
    }
}

// Generate new CSRF token for the form
$_SESSION['csrf_token'] = $csrf_token;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login | <?= APP_NAME ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/login.css">
</head>
<body class="login-body">
    <div class="login-container">
        <div class="login-card">
            <div class="text-center mb-4">
                <img src="assets/images/logo.png" alt="DOWN-RWANDA" class="login-logo">
                <h2 class="login-title">Event Tracking System</h2>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
            <?php endif; ?>

            <form method="POST" action="login.php" class="login-form">
                <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                
                <div class="mb-3">
                    <label for="username" class="form-label">Username</label>
                    <input type="text" class="form-control" id="username" name="username" 
                           value="<?= htmlspecialchars($username) ?>" required autofocus>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="remember" name="remember">
                    <label class="form-check-label" for="remember">Remember me</label>
                </div>
                
                <button type="submit" class="btn btn-primary w-100 login-btn">Login</button>
                
                <div class="mt-3 text-center">
                    <a href="forgot_password.php" class="login-link">Forgot password?</a>
                    <span class="mx-2">|</span>
                    <a href="register.php" class="login-link">Create account</a>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Client-side validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            
            if (!username || !password) {
                e.preventDefault();
                alert('Please fill in all fields');
            }
        });
    </script>
</body>
</html>