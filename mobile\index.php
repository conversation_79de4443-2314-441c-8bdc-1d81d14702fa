<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require '../includes/db_connect.php';
require '../includes/auth_check.php';
require '../includes/functions.php';

// Get user's recent events
$userEvents = $pdo->prepare("
    SELECT e.*, ea.views
    FROM events e
    LEFT JOIN event_analytics ea ON e.id = ea.event_id
    WHERE e.user_id = ?
    ORDER BY e.created_at DESC
    LIMIT 5
");
$userEvents->execute([$_SESSION['user_id']]);
$recentEvents = $userEvents->fetchAll();

// Get nearby events
$nearbyEvents = [];
if (isset($_GET['lat']) && isset($_GET['lng'])) {
    $lat = floatval($_GET['lat']);
    $lng = floatval($_GET['lng']);

    $nearbyStmt = $pdo->prepare("
        SELECT e.*, u.username,
               ST_Distance_Sphere(POINT(?, ?), POINT(e.longitude, e.latitude)) as distance
        FROM events e
        LEFT JOIN users u ON e.user_id = u.id
        WHERE e.is_approved = TRUE
        AND ST_Distance_Sphere(POINT(?, ?), POINT(e.longitude, e.latitude)) <= 5000
        ORDER BY distance ASC
        LIMIT 10
    ");
    $nearbyStmt->execute([$lng, $lat, $lng, $lat]);
    $nearbyEvents = $nearbyStmt->fetchAll();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#2c3e50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="DOWN-RWANDA">

    <title>DOWN-RWANDA Mobile</title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    <link rel="apple-touch-icon" href="../assets/icons/icon-192x192.png">

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/leaflet@1.9.4/dist/leaflet.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --safe-area-inset-top: env(safe-area-inset-top);
            --safe-area-inset-bottom: env(safe-area-inset-bottom);
        }

        * {
            -webkit-tap-highlight-color: transparent;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding-top: var(--safe-area-inset-top);
            padding-bottom: var(--safe-area-inset-bottom);
            overflow-x: hidden;
        }

        .mobile-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: 1rem;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin: 1rem;
        }

        .action-card {
            background: white;
            border-radius: 20px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: none;
            text-decoration: none;
            color: inherit;
        }

        .action-card:hover, .action-card:active {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
            color: inherit;
            text-decoration: none;
        }

        .action-card.emergency {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
        }

        .action-card.report {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white;
        }

        .action-card.map {
            background: linear-gradient(135deg, #45b7d1, #96c93d);
            color: white;
        }

        .action-card.profile {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
        }

        .action-icon {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .action-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 0.25rem;
        }

        .action-subtitle {
            font-size: 0.85rem;
            opacity: 0.8;
        }

        .section-card {
            background: white;
            border-radius: 20px;
            margin: 1rem;
            padding: 1.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .event-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 15px;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .event-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .event-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: white;
        }

        .event-icon.accident { background: var(--danger-color); }
        .event-icon.show { background: var(--success-color); }
        .event-icon.party { background: var(--secondary-color); }
        .event-icon.fire { background: #fd7e14; }
        .event-icon.medical { background: #e83e8c; }

        .event-content {
            flex: 1;
        }

        .event-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
            font-size: 0.95rem;
        }

        .event-meta {
            font-size: 0.8rem;
            color: #6c757d;
        }

        .floating-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--danger-color), #c0392b);
            color: white;
            border: none;
            box-shadow: 0 10px 30px rgba(231, 76, 60, 0.4);
            font-size: 1.5rem;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 15px 40px rgba(231, 76, 60, 0.6);
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            padding: 0.5rem 0;
            padding-bottom: calc(0.5rem + var(--safe-area-inset-bottom));
            z-index: 1000;
        }

        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0.5rem;
            color: #6c757d;
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 10px;
        }

        .nav-item.active, .nav-item:hover {
            color: var(--primary-color);
            background: rgba(44, 62, 80, 0.1);
        }

        .nav-icon {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        .nav-label {
            font-size: 0.7rem;
            font-weight: 500;
        }

        .mini-map {
            height: 200px;
            border-radius: 15px;
            overflow: hidden;
            margin-top: 1rem;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .status-online { background: var(--success-color); }
        .status-busy { background: var(--warning-color); }
        .status-offline { background: var(--danger-color); }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .slide-up {
            animation: slideUp 0.5s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            body {
                background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            }

            .mobile-header, .bottom-nav {
                background: rgba(52, 73, 94, 0.95);
                color: white;
            }

            .section-card {
                background: #34495e;
                color: white;
            }

            .event-item {
                background: #2c3e50;
                color: white;
            }

            .event-item:hover {
                background: #1a252f;
            }
        }

        /* Responsive adjustments */
        @media (max-width: 576px) {
            .quick-actions {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .action-card {
                padding: 1rem;
            }

            .section-card {
                margin: 0.5rem;
                padding: 1rem;
            }
        }

        /* Loading states */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="mobile-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-0">
                    <span class="status-indicator status-online pulse"></span>
                    DOWN-RWANDA
                </h5>
                <small class="text-muted">Welcome, <?= htmlspecialchars($_SESSION['username']) ?></small>
            </div>
            <div>
                <button class="btn btn-sm btn-outline-primary" onclick="requestLocation()">
                    <i class="fas fa-location-arrow"></i>
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="toggleNotifications()">
                    <i class="fas fa-bell"></i>
                    <span class="badge bg-danger" id="notificationBadge" style="display: none;">0</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <a href="#" class="action-card emergency pulse" onclick="reportEmergency()">
            <i class="action-icon fas fa-exclamation-triangle"></i>
            <div class="action-title">Emergency</div>
            <div class="action-subtitle">Report accident</div>
        </a>

        <a href="report.php" class="action-card report">
            <i class="action-icon fas fa-plus-circle"></i>
            <div class="action-title">Report Event</div>
            <div class="action-subtitle">Add new event</div>
        </a>

        <a href="#" class="action-card map" onclick="showMap()">
            <i class="action-icon fas fa-map-marked-alt"></i>
            <div class="action-title">Live Map</div>
            <div class="action-subtitle">View events</div>
        </a>

        <a href="profile.php" class="action-card profile">
            <i class="action-icon fas fa-user-circle"></i>
            <div class="action-title">Profile</div>
            <div class="action-subtitle">Your account</div>
        </a>
    </div>

    <!-- Recent Events -->
    <?php if (!empty($recentEvents)): ?>
    <div class="section-card slide-up">
        <h6 class="section-title">
            <i class="fas fa-history me-2"></i>
            Your Recent Events
        </h6>
        <?php foreach ($recentEvents as $event): ?>
        <div class="event-item" onclick="viewEvent(<?= $event['id'] ?>)">
            <div class="event-icon <?= $event['type'] ?>">
                <i class="fas fa-<?= getEventIcon($event['type']) ?>"></i>
            </div>
            <div class="event-content">
                <div class="event-title"><?= htmlspecialchars($event['title']) ?></div>
                <div class="event-meta">
                    <?= formatTimeAgo($event['created_at']) ?>
                    <?php if ($event['views']): ?>
                    • <?= $event['views'] ?> views
                    <?php endif; ?>
                </div>
            </div>
            <i class="fas fa-chevron-right text-muted"></i>
        </div>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>

    <!-- Nearby Events -->
    <?php if (!empty($nearbyEvents)): ?>
    <div class="section-card slide-up">
        <h6 class="section-title">
            <i class="fas fa-map-marker-alt me-2"></i>
            Nearby Events
        </h6>
        <?php foreach ($nearbyEvents as $event): ?>
        <div class="event-item" onclick="viewEvent(<?= $event['id'] ?>)">
            <div class="event-icon <?= $event['type'] ?>">
                <i class="fas fa-<?= getEventIcon($event['type']) ?>"></i>
            </div>
            <div class="event-content">
                <div class="event-title"><?= htmlspecialchars($event['title']) ?></div>
                <div class="event-meta">
                    <?= round($event['distance'] / 1000, 1) ?>km away •
                    by <?= htmlspecialchars($event['username']) ?>
                </div>
            </div>
            <i class="fas fa-chevron-right text-muted"></i>
        </div>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>

    <!-- Mini Map -->
    <div class="section-card slide-up">
        <h6 class="section-title">
            <i class="fas fa-globe me-2"></i>
            Event Map
        </h6>
        <div id="miniMap" class="mini-map"></div>
    </div>

    <!-- Emergency Button -->
    <button class="floating-btn" onclick="reportEmergency()" title="Emergency Report">
        <i class="fas fa-exclamation-triangle"></i>
    </button>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <div class="nav-items">
            <a href="index.php" class="nav-item active">
                <i class="nav-icon fas fa-home"></i>
                <span class="nav-label">Home</span>
            </a>
            <a href="map.php" class="nav-item">
                <i class="nav-icon fas fa-map"></i>
                <span class="nav-label">Map</span>
            </a>
            <a href="report.php" class="nav-item">
                <i class="nav-icon fas fa-plus"></i>
                <span class="nav-label">Report</span>
            </a>
            <a href="notifications.php" class="nav-item">
                <i class="nav-icon fas fa-bell"></i>
                <span class="nav-label">Alerts</span>
            </a>
            <a href="profile.php" class="nav-item">
                <i class="nav-icon fas fa-user"></i>
                <span class="nav-label">Profile</span>
            </a>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="mobile-app.js"></script>
</body>
</html>

<?php
function getEventIcon($type) {
    $icons = [
        'accident' => 'car-crash',
        'fire' => 'fire',
        'medical' => 'ambulance',
        'crime' => 'shield-alt',
        'show' => 'music',
        'party' => 'glass-cheers',
        'sports' => 'futbol'
    ];
    return $icons[$type] ?? 'calendar-alt';
}
?>
