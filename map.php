<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require 'includes/db_connect.php';
require 'includes/auth_check.php';
require 'includes/functions.php';
require 'config.php';

// Get URL parameters for initial view
$lat = isset($_GET['lat']) ? floatval($_GET['lat']) : -1.9403;
$lng = isset($_GET['lng']) ? floatval($_GET['lng']) : 29.8739;
$zoom = isset($_GET['zoom']) ? intval($_GET['zoom']) : 12;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Map | <?= APP_NAME ?></title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/leaflet@1.9.4/dist/leaflet.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 5px 20px rgba(0, 0, 0, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
        }

        #map {
            height: 100vh;
            width: 100%;
        }

        .map-controls {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .control-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1rem;
            box-shadow: var(--shadow-medium);
            min-width: 250px;
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            width: 100%;
            margin-bottom: 0.5rem;
        }

        .control-btn:hover {
            background: var(--secondary-color);
            color: white;
            transform: translateY(-1px);
        }

        .control-btn.active {
            background: var(--primary-color);
            color: white;
        }

        .filter-section {
            margin-bottom: 1rem;
        }

        .filter-title {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .filter-chips {
            display: flex;
            flex-wrap: wrap;
            gap: 0.25rem;
        }

        .filter-chip {
            background: #e9ecef;
            border: none;
            border-radius: 15px;
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .filter-chip:hover {
            background: var(--secondary-color);
            color: white;
        }

        .filter-chip.active {
            background: var(--primary-color);
            color: white;
        }

        .stats-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1rem;
            box-shadow: var(--shadow-medium);
            z-index: 1000;
            min-width: 200px;
        }

        .stats-title {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stat-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            font-size: 0.85rem;
        }

        .stat-label {
            color: #6c757d;
        }

        .stat-value {
            font-weight: 600;
            color: var(--primary-color);
        }

        .legend {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1rem;
            box-shadow: var(--shadow-medium);
            z-index: 1000;
        }

        .legend-title {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.75rem;
            font-size: 0.9rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            font-size: 0.8rem;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
            border: 2px solid white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .floating-actions {
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background: var(--secondary-color);
            color: white;
            box-shadow: var(--shadow-medium);
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .floating-btn:hover {
            background: var(--primary-color);
            transform: scale(1.1);
        }

        .floating-btn.danger {
            background: var(--danger-color);
        }

        .floating-btn.success {
            background: var(--success-color);
        }

        .search-panel {
            position: absolute;
            top: 80px;
            left: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1rem;
            box-shadow: var(--shadow-medium);
            z-index: 1000;
            transform: translateY(-100%);
            opacity: 0;
            transition: all 0.3s ease;
            pointer-events: none;
        }

        .search-panel.show {
            transform: translateY(0);
            opacity: 1;
            pointer-events: all;
        }

        .search-input {
            width: 100%;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 0.75rem;
            font-size: 0.9rem;
        }

        .search-input:focus {
            border-color: var(--secondary-color);
            outline: none;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            flex-direction: column;
            gap: 1rem;
        }

        .loading-overlay.hidden {
            display: none;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e9ecef;
            border-top: 4px solid var(--secondary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Custom marker styles */
        .custom-marker {
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .marker-pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .map-controls {
                top: 10px;
                left: 10px;
                right: 10px;
            }

            .control-panel {
                min-width: auto;
                width: 100%;
            }

            .stats-panel {
                top: 10px;
                right: 10px;
                min-width: 150px;
            }

            .legend {
                bottom: 80px;
                right: 10px;
                left: 10px;
            }

            .floating-actions {
                bottom: 10px;
                left: 10px;
            }

            .search-panel {
                top: 60px;
                left: 10px;
                right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner"></div>
        <p>Loading events...</p>
    </div>

    <!-- Map -->
    <div id="map"></div>

    <!-- Map Controls -->
    <div class="map-controls">
        <div class="control-panel">
            <button class="control-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
                Back to Dashboard
            </button>

            <button class="control-btn" onclick="toggleSearch()">
                <i class="fas fa-search"></i>
                Search Events
            </button>

            <button class="control-btn" onclick="refreshMap()">
                <i class="fas fa-sync-alt"></i>
                Refresh Map
            </button>

            <button class="control-btn" onclick="toggleFullscreen()">
                <i class="fas fa-expand"></i>
                Fullscreen
            </button>

            <!-- Filters -->
            <div class="filter-section">
                <div class="filter-title">Event Types</div>
                <div class="filter-chips">
                    <button class="filter-chip active" data-filter="all" onclick="setFilter('all')">All</button>
                    <button class="filter-chip" data-filter="accident" onclick="setFilter('accident')">Accidents</button>
                    <button class="filter-chip" data-filter="fire" onclick="setFilter('fire')">Fire</button>
                    <button class="filter-chip" data-filter="medical" onclick="setFilter('medical')">Medical</button>
                    <button class="filter-chip" data-filter="crime" onclick="setFilter('crime')">Crime</button>
                </div>
            </div>

            <div class="filter-section">
                <div class="filter-title">Severity</div>
                <div class="filter-chips">
                    <button class="filter-chip active" data-severity="all" onclick="setSeverityFilter('all')">All</button>
                    <button class="filter-chip" data-severity="critical" onclick="setSeverityFilter('critical')">Critical</button>
                    <button class="filter-chip" data-severity="high" onclick="setSeverityFilter('high')">High</button>
                    <button class="filter-chip" data-severity="medium" onclick="setSeverityFilter('medium')">Medium</button>
                    <button class="filter-chip" data-severity="low" onclick="setSeverityFilter('low')">Low</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Panel -->
    <div class="search-panel" id="searchPanel">
        <input type="text" class="search-input" id="searchInput"
               placeholder="Search events by title, description, or location..."
               onkeyup="searchEvents(this.value)">
    </div>

    <!-- Stats Panel -->
    <div class="stats-panel">
        <div class="stats-title">
            <i class="fas fa-chart-bar"></i>
            Live Statistics
        </div>
        <div class="stat-row">
            <span class="stat-label">Total Events:</span>
            <span class="stat-value" id="totalEvents">0</span>
        </div>
        <div class="stat-row">
            <span class="stat-label">Visible:</span>
            <span class="stat-value" id="visibleEvents">0</span>
        </div>
        <div class="stat-row">
            <span class="stat-label">Critical:</span>
            <span class="stat-value" id="criticalEvents">0</span>
        </div>
        <div class="stat-row">
            <span class="stat-label">Last Update:</span>
            <span class="stat-value" id="lastUpdate">--:--</span>
        </div>
    </div>

    <!-- Legend -->
    <div class="legend">
        <div class="legend-title">Event Types</div>
        <div class="legend-item">
            <div class="legend-color" style="background: #e74c3c;"></div>
            <span>Accident</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #fd7e14;"></div>
            <span>Fire</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #e83e8c;"></div>
            <span>Medical</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #6f42c1;"></div>
            <span>Crime</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #27ae60;"></div>
            <span>Event</span>
        </div>
    </div>

    <!-- Floating Actions -->
    <div class="floating-actions">
        <button class="floating-btn" onclick="goToMyLocation()" title="My Location">
            <i class="fas fa-location-arrow"></i>
        </button>
        <button class="floating-btn success" onclick="reportEvent()" title="Report Event">
            <i class="fas fa-plus"></i>
        </button>
        <button class="floating-btn danger" onclick="emergencyAlert()" title="Emergency Alert">
            <i class="fas fa-exclamation-triangle"></i>
        </button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.4/dist/leaflet.js"></script>

    <script>
        let map;
        let markers = [];
        let allEvents = [];
        let currentFilter = 'all';
        let currentSeverityFilter = 'all';
        let currentLocation = null;

        // Initialize map
        function initMap() {
            map = L.map('map').setView([<?= $lat ?>, <?= $lng ?>], <?= $zoom ?>);

            // Add tile layers
            const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 18
            });

            const satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: '© Esri',
                maxZoom: 18
            });

            // Add default layer
            osmLayer.addTo(map);

            // Layer control
            const baseLayers = {
                "Street Map": osmLayer,
                "Satellite": satelliteLayer
            };

            L.control.layers(baseLayers).addTo(map);

            // Get user location
            getCurrentLocation();

            // Load events
            loadEvents();

            // Auto-refresh every 30 seconds
            setInterval(loadEvents, 30000);
        }

        function getCurrentLocation() {
            if ('geolocation' in navigator) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        currentLocation = {
                            lat: position.coords.latitude,
                            lng: position.coords.longitude
                        };

                        // Add current location marker
                        const currentLocationIcon = L.divIcon({
                            html: '<div style="background: #007bff; width: 16px; height: 16px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>',
                            className: 'current-location-marker',
                            iconSize: [16, 16],
                            iconAnchor: [8, 8]
                        });

                        L.marker([currentLocation.lat, currentLocation.lng], {
                            icon: currentLocationIcon
                        }).addTo(map).bindPopup('Your location');
                    },
                    (error) => {
                        console.error('Geolocation error:', error);
                    }
                );
            }
        }

        async function loadEvents() {
            try {
                const response = await fetch('api/v2/events?limit=200');
                const result = await response.json();

                if (result.success) {
                    allEvents = result.data;
                    updateMap();
                    updateStats();
                } else {
                    console.error('Failed to load events:', result.error);
                }
            } catch (error) {
                console.error('Error loading events:', error);
            } finally {
                document.getElementById('loadingOverlay').classList.add('hidden');
            }
        }

        function updateMap() {
            // Clear existing markers
            markers.forEach(marker => map.removeLayer(marker));
            markers = [];

            // Filter events
            const filteredEvents = allEvents.filter(event => {
                if (currentFilter !== 'all' && event.type !== currentFilter) return false;
                if (currentSeverityFilter !== 'all' && event.severity_level !== currentSeverityFilter) return false;
                return true;
            });

            // Add event markers
            filteredEvents.forEach(event => {
                const marker = createEventMarker(event);
                markers.push(marker);
                marker.addTo(map);
            });

            // Update visible events count
            document.getElementById('visibleEvents').textContent = filteredEvents.length;
        }

        function createEventMarker(event) {
            const colors = {
                'accident': '#e74c3c',
                'fire': '#fd7e14',
                'medical': '#e83e8c',
                'crime': '#6f42c1',
                'show': '#27ae60',
                'party': '#17a2b8',
                'sports': '#ffc107'
            };

            const color = colors[event.type] || '#6c757d';
            const size = event.severity_level === 'critical' ? 24 : 20;
            const pulseClass = event.severity_level === 'critical' ? 'marker-pulse' : '';

            const icon = L.divIcon({
                html: `<div class="custom-marker ${pulseClass}" style="background-color: ${color}; width: ${size}px; height: ${size}px; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px; font-weight: bold;">
                    ${event.severity_level === 'critical' ? '!' : ''}
                </div>`,
                className: 'event-marker',
                iconSize: [size, size],
                iconAnchor: [size/2, size/2]
            });

            const marker = L.marker([event.latitude, event.longitude], { icon });

            const popupContent = `
                <div style="min-width: 250px; max-width: 300px;">
                    <h6 style="margin-bottom: 8px; color: #2c3e50;">${event.title}</h6>
                    <p style="margin-bottom: 8px; font-size: 0.9rem;">${event.description || 'No description available'}</p>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <span class="badge" style="background-color: ${color}; color: white; font-size: 10px;">${event.type}</span>
                        <span class="badge" style="background-color: ${getSeverityColor(event.severity_level)}; color: white; font-size: 10px;">${event.severity_level}</span>
                    </div>
                    <small style="color: #6c757d;">
                        ${event.time_ago} • by ${event.reporter_name}
                        ${event.views ? `• ${event.views} views` : ''}
                    </small>
                    <div style="margin-top: 10px;">
                        <button onclick="viewEventDetails(${event.id})" class="btn btn-sm btn-primary">View Details</button>
                        <button onclick="getDirections(${event.latitude}, ${event.longitude})" class="btn btn-sm btn-outline-secondary">Directions</button>
                    </div>
                </div>
            `;

            marker.bindPopup(popupContent);

            return marker;
        }

        function getSeverityColor(severity) {
            const colors = {
                'critical': '#e74c3c',
                'high': '#f39c12',
                'medium': '#3498db',
                'low': '#27ae60'
            };
            return colors[severity] || '#6c757d';
        }

        function updateStats() {
            const total = allEvents.length;
            const critical = allEvents.filter(e => e.severity_level === 'critical').length;

            document.getElementById('totalEvents').textContent = total;
            document.getElementById('criticalEvents').textContent = critical;
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
        }

        function setFilter(filter) {
            currentFilter = filter;

            // Update UI
            document.querySelectorAll('[data-filter]').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

            updateMap();
        }

        function setSeverityFilter(severity) {
            currentSeverityFilter = severity;

            // Update UI
            document.querySelectorAll('[data-severity]').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-severity="${severity}"]`).classList.add('active');

            updateMap();
        }

        function toggleSearch() {
            const panel = document.getElementById('searchPanel');
            panel.classList.toggle('show');

            if (panel.classList.contains('show')) {
                document.getElementById('searchInput').focus();
            }
        }

        function searchEvents(query) {
            if (!query.trim()) {
                updateMap();
                return;
            }

            // Clear existing markers
            markers.forEach(marker => map.removeLayer(marker));
            markers = [];

            // Filter events by search query
            const filteredEvents = allEvents.filter(event => {
                const searchText = `${event.title} ${event.description} ${event.location_description}`.toLowerCase();
                return searchText.includes(query.toLowerCase());
            });

            // Add filtered markers
            filteredEvents.forEach(event => {
                const marker = createEventMarker(event);
                markers.push(marker);
                marker.addTo(map);
            });

            document.getElementById('visibleEvents').textContent = filteredEvents.length;
        }

        function refreshMap() {
            document.getElementById('loadingOverlay').classList.remove('hidden');
            loadEvents();
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        function goToMyLocation() {
            if (currentLocation) {
                map.setView([currentLocation.lat, currentLocation.lng], 16);
            } else {
                getCurrentLocation();
            }
        }

        function reportEvent() {
            window.open('report_event.php', '_blank');
        }

        function emergencyAlert() {
            if (confirm('Send emergency alert to authorities?')) {
                // Implement emergency alert functionality
                alert('Emergency alert sent!');
            }
        }

        function goBack() {
            window.history.back();
        }

        function viewEventDetails(eventId) {
            window.open(`event.php?id=${eventId}`, '_blank');
        }

        function getDirections(lat, lng) {
            if (currentLocation) {
                const url = `https://www.google.com/maps/dir/${currentLocation.lat},${currentLocation.lng}/${lat},${lng}`;
                window.open(url, '_blank');
            } else {
                const url = `https://www.google.com/maps/search/${lat},${lng}`;
                window.open(url, '_blank');
            }
        }

        // Initialize map when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
        });

        // Handle fullscreen changes
        document.addEventListener('fullscreenchange', function() {
            setTimeout(() => {
                if (map) {
                    map.invalidateSize();
                }
            }, 100);
        });

        // Close search panel when clicking outside
        document.addEventListener('click', function(e) {
            const panel = document.getElementById('searchPanel');
            const searchBtn = document.querySelector('[onclick="toggleSearch()"]');

            if (!panel.contains(e.target) && !searchBtn.contains(e.target)) {
                panel.classList.remove('show');
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                document.getElementById('searchPanel').classList.remove('show');
            }
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                toggleSearch();
            }
            if (e.key === 'F11') {
                e.preventDefault();
                toggleFullscreen();
            }
        });
    </script>
</body>
</html>
