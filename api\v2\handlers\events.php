<?php

function handleEvents($method, $id, $user) {
    global $pdo;

    switch ($method) {
        case 'GET':
            if ($id) {
                getEvent($id, $user);
            } else {
                getEvents($user);
            }
            break;

        case 'POST':
            createEvent($user);
            break;

        case 'PUT':
            if (!$id) {
                http_response_code(400);
                echo json_encode(['error' => 'Event ID required']);
                return;
            }
            updateEvent($id, $user);
            break;

        case 'DELETE':
            if (!$id) {
                http_response_code(400);
                echo json_encode(['error' => 'Event ID required']);
                return;
            }
            deleteEvent($id, $user);
            break;

        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
}

function getEvents($user) {
    global $pdo, $EVENT_TYPES;

    $page = intval($_GET['page'] ?? 1);
    $limit = min(intval($_GET['limit'] ?? 20), 100); // Max 100 items per page
    $offset = ($page - 1) * $limit;

    $type = sanitizeInput($_GET['type'] ?? '');
    $status = sanitizeInput($_GET['status'] ?? '');
    $lat = floatval($_GET['lat'] ?? 0);
    $lng = floatval($_GET['lng'] ?? 0);
    $radius = intval($_GET['radius'] ?? 10); // km
    $severity = sanitizeInput($_GET['severity'] ?? '');
    $dateFrom = sanitizeInput($_GET['date_from'] ?? '');
    $dateTo = sanitizeInput($_GET['date_to'] ?? '');

    $cacheKey = getCacheKey('events', [
        'user_role' => $user['role'],
        'page' => $page,
        'limit' => $limit,
        'type' => $type,
        'status' => $status,
        'lat' => $lat,
        'lng' => $lng,
        'radius' => $radius,
        'severity' => $severity,
        'date_from' => $dateFrom,
        'date_to' => $dateTo
    ]);

    $cachedResult = getCache($cacheKey);
    if ($cachedResult !== false) {
        echo json_encode($cachedResult);
        return;
    }

    try {
        $whereConditions = [];
        $params = [];

        // Base query
        $query = "
            SELECT e.*, u.username as reporter_name, u.role as reporter_role,
                   ea.views, ea.shares, ea.severity_score,
                   COUNT(ei.id) as image_count
            FROM events e
            LEFT JOIN users u ON e.user_id = u.id
            LEFT JOIN event_analytics ea ON e.id = ea.event_id
            LEFT JOIN event_images ei ON e.id = ei.event_id
        ";

        // Apply filters based on user role
        if ($user['role'] !== 'admin') {
            $whereConditions[] = "e.is_approved = TRUE";
        }

        if ($type && isset($EVENT_TYPES[$type])) {
            $whereConditions[] = "e.type = ?";
            $params[] = $type;
        }

        if ($status === 'pending') {
            $whereConditions[] = "e.is_approved = FALSE";
        } elseif ($status === 'approved') {
            $whereConditions[] = "e.is_approved = TRUE";
        }

        if ($severity) {
            $whereConditions[] = "e.severity_level = ?";
            $params[] = $severity;
        }

        if ($dateFrom) {
            $whereConditions[] = "e.created_at >= ?";
            $params[] = $dateFrom . ' 00:00:00';
        }

        if ($dateTo) {
            $whereConditions[] = "e.created_at <= ?";
            $params[] = $dateTo . ' 23:59:59';
        }

        // Location-based filtering
        if ($lat && $lng && $radius) {
            $whereConditions[] = "ST_Distance_Sphere(POINT(?, ?), POINT(e.longitude, e.latitude)) <= ?";
            array_push($params, $lng, $lat, $radius * 1000);
        }

        if (!empty($whereConditions)) {
            $query .= " WHERE " . implode(" AND ", $whereConditions);
        }

        $query .= " GROUP BY e.id ORDER BY e.created_at DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;

        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        $events = $stmt->fetchAll();

        // Get total count for pagination
        $countQuery = "SELECT COUNT(DISTINCT e.id) as total FROM events e";
        if (!empty($whereConditions)) {
            $countQuery .= " WHERE " . implode(" AND ", array_slice($whereConditions, 0, -1)); // Remove location condition for count
        }

        $countStmt = $pdo->prepare($countQuery);
        $countStmt->execute(array_slice($params, 0, -2)); // Remove limit and offset
        $totalCount = $countStmt->fetch()['total'];

        // Enhance events with additional data
        foreach ($events as &$event) {
            $event['type_info'] = $EVENT_TYPES[$event['type']] ?? null;
            $event['time_ago'] = formatTimeAgo($event['created_at']);
            $event['distance'] = null;

            if ($lat && $lng) {
                $event['distance'] = round(getDistance($lat, $lng, $event['latitude'], $event['longitude']), 2);
            }

            // Track view
            trackEventView($event['id'], $user['user_id']);
        }

        $result = [
            'success' => true,
            'data' => $events,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => intval($totalCount),
                'pages' => ceil($totalCount / $limit)
            ],
            'filters_applied' => [
                'type' => $type,
                'status' => $status,
                'severity' => $severity,
                'location' => $lat && $lng ? ['lat' => $lat, 'lng' => $lng, 'radius' => $radius] : null,
                'date_range' => $dateFrom || $dateTo ? ['from' => $dateFrom, 'to' => $dateTo] : null
            ]
        ];

        setCache($cacheKey, $result, 60); // Cache for 1 minute
        echo json_encode($result);

    } catch (PDOException $e) {
        logError("Failed to get events: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to retrieve events']);
    }
}

function getEvent($id, $user) {
    global $pdo, $EVENT_TYPES;

    try {
        $stmt = $pdo->prepare("
            SELECT e.*, u.username as reporter_name, u.role as reporter_role, u.phone as reporter_phone,
                   ea.views, ea.shares, ea.severity_score, ea.response_time
            FROM events e
            LEFT JOIN users u ON e.user_id = u.id
            LEFT JOIN event_analytics ea ON e.id = ea.event_id
            WHERE e.id = ?
        ");
        $stmt->execute([$id]);
        $event = $stmt->fetch();

        if (!$event) {
            http_response_code(404);
            echo json_encode(['error' => 'Event not found']);
            return;
        }

        // Check permissions
        if (!$event['is_approved'] && $user['role'] !== 'admin' && $event['user_id'] != $user['user_id']) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        // Get event images
        $imageStmt = $pdo->prepare("SELECT * FROM event_images WHERE event_id = ?");
        $imageStmt->execute([$id]);
        $event['images'] = $imageStmt->fetchAll();

        // Get emergency responses
        $responseStmt = $pdo->prepare("
            SELECT er.*, pu.unit_name, u.username as officer_name
            FROM emergency_responses er
            LEFT JOIN police_units pu ON er.police_unit_id = pu.id
            LEFT JOIN users u ON pu.officer_id = u.id
            WHERE er.event_id = ?
            ORDER BY er.created_at DESC
        ");
        $responseStmt->execute([$id]);
        $event['emergency_responses'] = $responseStmt->fetchAll();

        // Enhance event data
        $event['type_info'] = $EVENT_TYPES[$event['type']] ?? null;
        $event['time_ago'] = formatTimeAgo($event['created_at']);

        // AI analysis if available
        if ($event['images']) {
            foreach ($event['images'] as &$image) {
                if ($image['ai_analysis']) {
                    $image['ai_analysis'] = json_decode($image['ai_analysis'], true);
                }
            }
        }

        // Track view
        trackEventView($id, $user['user_id']);

        echo json_encode([
            'success' => true,
            'data' => $event
        ]);

    } catch (PDOException $e) {
        logError("Failed to get event: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to retrieve event']);
    }
}

function createEvent($user) {
    global $pdo, $EVENT_TYPES;

    $input = json_decode(file_get_contents('php://input'), true);

    $title = sanitizeInput($input['title'] ?? '');
    $type = sanitizeInput($input['type'] ?? '');
    $description = sanitizeInput($input['description'] ?? '');
    $latitude = floatval($input['latitude'] ?? 0);
    $longitude = floatval($input['longitude'] ?? 0);
    $severityLevel = sanitizeInput($input['severity_level'] ?? 'medium');

    // Validation
    if (!$title || !$type || !$latitude || !$longitude) {
        http_response_code(400);
        echo json_encode(['error' => 'Title, type, latitude, and longitude are required']);
        return;
    }

    if (!isset($EVENT_TYPES[$type])) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid event type']);
        return;
    }

    if (!validateCoordinates($latitude, $longitude)) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid coordinates']);
        return;
    }

    $eventConfig = $EVENT_TYPES[$type];

    // Check payment requirement
    $requiresPayment = $eventConfig['requires_payment'] && $user['role'] === 'organization';
    $isApproved = $eventConfig['auto_approve'] || $user['role'] === 'admin';

    try {
        $pdo->beginTransaction();

        // Auto-categorize if not provided
        if (!$description) {
            $suggestedType = categorizeEventAutomatically($title, $description);
            if ($suggestedType !== 'other' && $suggestedType !== $type) {
                logActivity("Event type suggestion: $suggestedType for event: $title");
            }
        }

        // Insert event
        $stmt = $pdo->prepare("
            INSERT INTO events (
                title, type, description, latitude, longitude,
                severity_level, is_paid, is_approved, user_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $title, $type, $description, $latitude, $longitude,
            $severityLevel, $requiresPayment, $isApproved, $user['user_id']
        ]);

        $eventId = $pdo->lastInsertId();

        // Create analytics record
        $pdo->prepare("INSERT INTO event_analytics (event_id) VALUES (?)")
            ->execute([$eventId]);

        // Predict response time
        $predictedResponseTime = predictResponseTime($latitude, $longitude, $type);
        $pdo->prepare("UPDATE event_analytics SET response_time = ? WHERE event_id = ?")
            ->execute([$predictedResponseTime, $eventId]);

        // Auto-dispatch emergency services for critical events
        if (in_array($eventConfig['priority'], ['high', 'critical']) && $isApproved) {
            $dispatched = dispatchEmergencyServices($eventId, $latitude, $longitude, $type);
            logActivity("Auto-dispatched emergency services for event $eventId", 'INFO', [
                'dispatched_count' => count($dispatched)
            ]);
        }

        // Send notifications to nearby users
        $nearbyUsers = findNearbyUsers($latitude, $longitude, 5); // 5km radius
        $userIds = array_column($nearbyUsers, 'id');

        if (!empty($userIds)) {
            sendBulkNotification(
                $userIds,
                "New {$eventConfig['name']} reported near you: $title",
                $eventConfig['priority'] === 'critical' ? 'danger' : 'warning'
            );
        }

        $pdo->commit();

        echo json_encode([
            'success' => true,
            'data' => [
                'event_id' => $eventId,
                'is_approved' => $isApproved,
                'requires_payment' => $requiresPayment,
                'predicted_response_time' => $predictedResponseTime,
                'nearby_notifications_sent' => count($userIds)
            ]
        ]);

        logActivity("Event created via API", 'INFO', [
            'event_id' => $eventId,
            'type' => $type,
            'user_id' => $user['user_id']
        ]);

    } catch (PDOException $e) {
        $pdo->rollBack();
        logError("Failed to create event: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create event']);
    }
}

function updateEvent($id, $user) {
    global $pdo;

    $input = json_decode(file_get_contents('php://input'), true);

    // Check if user owns the event or has admin privileges
    $stmt = $pdo->prepare("SELECT user_id FROM events WHERE id = ?");
    $stmt->execute([$id]);
    $event = $stmt->fetch();

    if (!$event) {
        http_response_code(404);
        echo json_encode(['error' => 'Event not found']);
        return;
    }

    if ($event['user_id'] != $user['user_id'] && $user['role'] !== 'admin') {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        return;
    }

    try {
        $updateFields = [];
        $params = [];

        if (isset($input['title'])) {
            $updateFields[] = "title = ?";
            $params[] = sanitizeInput($input['title']);
        }

        if (isset($input['description'])) {
            $updateFields[] = "description = ?";
            $params[] = sanitizeInput($input['description']);
        }

        if (isset($input['severity_level'])) {
            $updateFields[] = "severity_level = ?";
            $params[] = sanitizeInput($input['severity_level']);
        }

        if (isset($input['is_approved']) && $user['role'] === 'admin') {
            $updateFields[] = "is_approved = ?";
            $params[] = (bool)$input['is_approved'];
        }

        if (empty($updateFields)) {
            http_response_code(400);
            echo json_encode(['error' => 'No valid fields to update']);
            return;
        }

        $params[] = $id;
        $sql = "UPDATE events SET " . implode(", ", $updateFields) . " WHERE id = ?";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        echo json_encode([
            'success' => true,
            'message' => 'Event updated successfully'
        ]);

        logActivity("Event updated via API", 'INFO', [
            'event_id' => $id,
            'user_id' => $user['user_id']
        ]);

    } catch (PDOException $e) {
        logError("Failed to update event: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update event']);
    }
}

function deleteEvent($id, $user) {
    global $pdo;

    // Check if user owns the event or has admin privileges
    $stmt = $pdo->prepare("SELECT user_id FROM events WHERE id = ?");
    $stmt->execute([$id]);
    $event = $stmt->fetch();

    if (!$event) {
        http_response_code(404);
        echo json_encode(['error' => 'Event not found']);
        return;
    }

    if ($event['user_id'] != $user['user_id'] && $user['role'] !== 'admin') {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        return;
    }

    try {
        $pdo->beginTransaction();

        // Archive the event before deletion
        $archiveStmt = $pdo->prepare("
            INSERT INTO archived_events (original_id, title, type, description, latitude, longitude, is_paid, payment_proof, is_approved, user_id, created_at)
            SELECT id, title, type, description, latitude, longitude, is_paid, payment_proof, is_approved, user_id, created_at
            FROM events WHERE id = ?
        ");
        $archiveStmt->execute([$id]);

        // Delete related records
        $pdo->prepare("DELETE FROM event_analytics WHERE event_id = ?")->execute([$id]);
        $pdo->prepare("DELETE FROM event_images WHERE event_id = ?")->execute([$id]);
        $pdo->prepare("DELETE FROM emergency_responses WHERE event_id = ?")->execute([$id]);

        // Delete the event
        $pdo->prepare("DELETE FROM events WHERE id = ?")->execute([$id]);

        $pdo->commit();

        echo json_encode([
            'success' => true,
            'message' => 'Event deleted and archived successfully'
        ]);

        logActivity("Event deleted via API", 'INFO', [
            'event_id' => $id,
            'user_id' => $user['user_id']
        ]);

    } catch (PDOException $e) {
        $pdo->rollBack();
        logError("Failed to delete event: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete event']);
    }
}
