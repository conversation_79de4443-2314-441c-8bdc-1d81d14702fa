<?php
// Security headers
header("X-Frame-Options: DENY");
header("X-Content-Type-Options: nosniff");
header("X-XSS-Protection: 1; mode=block");
header("Referrer-Policy: strict-origin-when-cross-origin");
header("Permissions-Policy: geolocation=(self), camera=(self), microphone=()");

// Application settings
define('APP_NAME', 'DOWN-RWANDA');
define('APP_VERSION', '2.0.0');
define('BASE_URL', 'http://' . $_SERVER['HTTP_HOST']);
define('API_BASE_URL', BASE_URL . '/api');
define('UPLOAD_DIR', __DIR__ . '/uploads/');
define('LOG_DIR', __DIR__ . '/logs/');
define('CACHE_DIR', __DIR__ . '/cache/');

// Advanced Security Settings
define('JWT_SECRET', 'your-super-secret-jwt-key-change-in-production');
define('JWT_EXPIRY', 3600); // 1 hour
define('SESSION_TIMEOUT', 1800); // 30 minutes
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOCKOUT_DURATION', 900); // 15 minutes

// API Rate Limiting
define('API_RATE_LIMIT', 100); // requests per hour
define('API_BURST_LIMIT', 20); // requests per minute

// Payment configuration
define('EVENT_FEE', 2000); // 2000 RWF
define('ALLOWED_FILE_TYPES', ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf']);
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('MAX_FILES_PER_EVENT', 5);

// Notification Settings
define('SMS_PROVIDER', 'twilio'); // twilio, nexmo, local
define('EMAIL_PROVIDER', 'smtp'); // smtp, sendgrid, mailgun
define('PUSH_NOTIFICATION_KEY', 'your-firebase-key');

// External API Settings
define('WEATHER_API_KEY', 'your-weather-api-key');
define('MAPS_API_KEY', 'your-google-maps-api-key');
define('TRANSLATION_API_KEY', 'your-translation-api-key');

// AI/ML Settings
define('AI_IMAGE_ANALYSIS', true);
define('AI_SEVERITY_DETECTION', true);
define('AI_RESPONSE_PREDICTION', true);

// Performance Settings
define('ENABLE_CACHING', true);
define('CACHE_DURATION', 300); // 5 minutes
define('ENABLE_COMPRESSION', true);
define('ENABLE_CDN', false);

// Monitoring & Analytics
define('ENABLE_ANALYTICS', true);
define('ANALYTICS_PROVIDER', 'google'); // google, custom
define('ERROR_REPORTING_LEVEL', E_ALL);
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR

// Emergency Response Settings
define('EMERGENCY_RESPONSE_RADIUS', 10000); // 10km in meters
define('POLICE_AUTO_DISPATCH', true);
define('AMBULANCE_AUTO_DISPATCH', false);
define('FIRE_DEPT_AUTO_DISPATCH', false);

// Create required directories
$directories = [
    UPLOAD_DIR,
    UPLOAD_DIR . 'events/',
    UPLOAD_DIR . 'payments/',
    UPLOAD_DIR . 'avatars/',
    UPLOAD_DIR . 'evidence/',
    LOG_DIR,
    CACHE_DIR,
    CACHE_DIR . 'api/',
    CACHE_DIR . 'images/',
    __DIR__ . '/temp/'
];

foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Organization codes (key => role)
$ORG_CODES = [
    'POLICE001' => 'police',
    'POLICE002' => 'police',
    'MEDIA001' => 'organization',
    'MEDIA002' => 'organization',
    'EVENTS001' => 'organization',
    'EVENTS002' => 'organization',
    'EVENTS003' => 'organization',
    'ADMIN001' => 'admin',
    'ADMIN002' => 'admin',
    'HOSPITAL001' => 'medical',
    'HOSPITAL002' => 'medical',
    'FIRE001' => 'fire_dept',
    'FIRE002' => 'fire_dept'
];

// Supported Languages
$SUPPORTED_LANGUAGES = [
    'en' => 'English',
    'rw' => 'Kinyarwanda',
    'fr' => 'Français',
    'sw' => 'Kiswahili'
];

// Event Types Configuration
$EVENT_TYPES = [
    'accident' => [
        'name' => 'Traffic Accident',
        'color' => '#dc3545',
        'icon' => 'fa-car-crash',
        'priority' => 'high',
        'auto_approve' => true,
        'requires_payment' => false,
        'emergency_services' => ['police', 'medical']
    ],
    'fire' => [
        'name' => 'Fire Emergency',
        'color' => '#fd7e14',
        'icon' => 'fa-fire',
        'priority' => 'critical',
        'auto_approve' => true,
        'requires_payment' => false,
        'emergency_services' => ['fire_dept', 'police', 'medical']
    ],
    'medical' => [
        'name' => 'Medical Emergency',
        'color' => '#e83e8c',
        'icon' => 'fa-ambulance',
        'priority' => 'critical',
        'auto_approve' => true,
        'requires_payment' => false,
        'emergency_services' => ['medical', 'police']
    ],
    'crime' => [
        'name' => 'Crime Report',
        'color' => '#6f42c1',
        'icon' => 'fa-shield-alt',
        'priority' => 'high',
        'auto_approve' => false,
        'requires_payment' => false,
        'emergency_services' => ['police']
    ],
    'show' => [
        'name' => 'Show/Concert',
        'color' => '#28a745',
        'icon' => 'fa-music',
        'priority' => 'low',
        'auto_approve' => false,
        'requires_payment' => true,
        'emergency_services' => []
    ],
    'party' => [
        'name' => 'Party/Event',
        'color' => '#17a2b8',
        'icon' => 'fa-glass-cheers',
        'priority' => 'low',
        'auto_approve' => false,
        'requires_payment' => true,
        'emergency_services' => []
    ],
    'sports' => [
        'name' => 'Sports Event',
        'color' => '#ffc107',
        'icon' => 'fa-futbol',
        'priority' => 'low',
        'auto_approve' => false,
        'requires_payment' => true,
        'emergency_services' => []
    ]
];

// Load environment-specific configuration
if (file_exists(__DIR__ . '/.env')) {
    $env = parse_ini_file(__DIR__ . '/.env');
    foreach ($env as $key => $value) {
        if (!defined($key)) {
            define($key, $value);
        }
    }
}

// Initialize error handling
if (defined('LOG_LEVEL')) {
    error_reporting(ERROR_REPORTING_LEVEL);
    ini_set('display_errors', LOG_LEVEL === 'DEBUG' ? 1 : 0);
    ini_set('log_errors', 1);
    ini_set('error_log', LOG_DIR . 'php_errors.log');
}
?>