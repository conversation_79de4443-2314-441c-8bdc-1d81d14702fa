<?php
/**
 * Database Structure Checker
 * Run this to verify your database is set up correctly
 */

require 'includes/db_connect.php';

echo "<h1>DOWN-RWANDA Database Structure Check</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
</style>";

$requiredTables = [
    'users' => [
        'id', 'username', 'email', 'password', 'role', 'phone', 'avatar', 
        'is_active', 'is_verified', 'created_at', 'updated_at'
    ],
    'events' => [
        'id', 'user_id', 'title', 'description', 'type', 'severity_level',
        'latitude', 'longitude', 'location_description', 'is_approved', 
        'is_active', 'created_at', 'updated_at'
    ],
    'event_images' => [
        'id', 'event_id', 'image_path', 'uploaded_at'
    ],
    'event_analytics' => [
        'id', 'event_id', 'views', 'shares', 'created_at', 'updated_at'
    ],
    'notifications' => [
        'id', 'user_id', 'message', 'type', 'is_read', 'created_at'
    ]
];

echo "<div class='section'>";
echo "<h2>Database Connection Test</h2>";
try {
    $pdo->query("SELECT 1");
    echo "<p class='success'>✓ Database connection successful</p>";
    
    // Get database name
    $dbName = $pdo->query("SELECT DATABASE()")->fetchColumn();
    echo "<p>Connected to database: <strong>$dbName</strong></p>";
    
} catch (PDOException $e) {
    echo "<p class='error'>✗ Database connection failed: " . $e->getMessage() . "</p>";
    exit;
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>Required Tables Check</h2>";
echo "<table>";
echo "<tr><th>Table</th><th>Status</th><th>Columns</th><th>Missing Columns</th></tr>";

foreach ($requiredTables as $tableName => $requiredColumns) {
    try {
        // Check if table exists
        $result = $pdo->query("SHOW TABLES LIKE '$tableName'")->fetch();
        
        if ($result) {
            echo "<tr>";
            echo "<td>$tableName</td>";
            echo "<td class='success'>✓ Exists</td>";
            
            // Get table columns
            $columns = $pdo->query("DESCRIBE $tableName")->fetchAll(PDO::FETCH_COLUMN);
            $missingColumns = array_diff($requiredColumns, $columns);
            
            echo "<td>" . count($columns) . " columns</td>";
            
            if (empty($missingColumns)) {
                echo "<td class='success'>All required columns present</td>";
            } else {
                echo "<td class='warning'>Missing: " . implode(', ', $missingColumns) . "</td>";
            }
            echo "</tr>";
            
        } else {
            echo "<tr>";
            echo "<td>$tableName</td>";
            echo "<td class='error'>✗ Missing</td>";
            echo "<td>-</td>";
            echo "<td class='error'>Table does not exist</td>";
            echo "</tr>";
        }
        
    } catch (PDOException $e) {
        echo "<tr>";
        echo "<td>$tableName</td>";
        echo "<td class='error'>✗ Error</td>";
        echo "<td>-</td>";
        echo "<td class='error'>" . $e->getMessage() . "</td>";
        echo "</tr>";
    }
}
echo "</table>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>Sample Data Check</h2>";

// Check for users
try {
    $userCount = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    echo "<p>Users in database: <strong>$userCount</strong></p>";
    
    if ($userCount == 0) {
        echo "<p class='warning'>⚠ No users found. You may need to register a user first.</p>";
    }
} catch (PDOException $e) {
    echo "<p class='error'>✗ Error checking users: " . $e->getMessage() . "</p>";
}

// Check for events
try {
    $eventCount = $pdo->query("SELECT COUNT(*) FROM events")->fetchColumn();
    echo "<p>Events in database: <strong>$eventCount</strong></p>";
} catch (PDOException $e) {
    echo "<p class='error'>✗ Error checking events: " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>Database Setup Instructions</h2>";

if (isset($_GET['create_tables'])) {
    echo "<h3>Creating Missing Tables...</h3>";
    
    try {
        $sql = file_get_contents('database_setup.sql');
        
        // Split SQL into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^(--|CREATE DATABASE|USE)/i', $statement)) {
                try {
                    $pdo->exec($statement);
                    echo "<p class='success'>✓ Executed: " . substr($statement, 0, 50) . "...</p>";
                } catch (PDOException $e) {
                    echo "<p class='warning'>⚠ Warning: " . $e->getMessage() . "</p>";
                }
            }
        }
        
        echo "<p class='success'><strong>Database setup completed! Please refresh this page to verify.</strong></p>";
        
    } catch (Exception $e) {
        echo "<p class='error'>✗ Error setting up database: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>If you see missing tables above, click the button below to create them:</p>";
    echo "<p><a href='?create_tables=1' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Create Missing Tables</a></p>";
    
    echo "<h3>Manual Setup (Alternative)</h3>";
    echo "<p>1. Import the <code>database_setup.sql</code> file into your MySQL database</p>";
    echo "<p>2. Or run the SQL commands manually in phpMyAdmin or MySQL command line</p>";
    echo "<p>3. Make sure your database connection settings in <code>includes/db_connect.php</code> are correct</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>Configuration Check</h2>";

// Check config file
if (file_exists('config.php')) {
    echo "<p class='success'>✓ config.php exists</p>";
    
    require 'config.php';
    
    if (defined('APP_NAME')) {
        echo "<p class='success'>✓ APP_NAME defined: " . APP_NAME . "</p>";
    } else {
        echo "<p class='error'>✗ APP_NAME not defined</p>";
    }
    
    if (isset($EVENT_TYPES) && is_array($EVENT_TYPES)) {
        echo "<p class='success'>✓ EVENT_TYPES defined (" . count($EVENT_TYPES) . " types)</p>";
    } else {
        echo "<p class='error'>✗ EVENT_TYPES not defined or not an array</p>";
    }
    
} else {
    echo "<p class='error'>✗ config.php missing</p>";
}

// Check functions file
if (file_exists('includes/functions.php')) {
    echo "<p class='success'>✓ includes/functions.php exists</p>";
} else {
    echo "<p class='error'>✗ includes/functions.php missing</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>File Permissions Check</h2>";

$directories = ['uploads', 'uploads/events', 'uploads/avatars', 'cache', 'logs'];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<p class='success'>✓ $dir is writable</p>";
        } else {
            echo "<p class='warning'>⚠ $dir exists but is not writable</p>";
        }
    } else {
        echo "<p class='warning'>⚠ $dir does not exist</p>";
        
        if (mkdir($dir, 0755, true)) {
            echo "<p class='success'>✓ Created $dir directory</p>";
        } else {
            echo "<p class='error'>✗ Failed to create $dir directory</p>";
        }
    }
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li>If all tables show as ✓ Exists, your database is ready</li>";
echo "<li>If any tables are missing, click 'Create Missing Tables' above</li>";
echo "<li>Make sure you have at least one user account (register if needed)</li>";
echo "<li>Try submitting an event through the report form</li>";
echo "<li>Check the error logs if you still have issues</li>";
echo "</ol>";
echo "</div>";
?>
