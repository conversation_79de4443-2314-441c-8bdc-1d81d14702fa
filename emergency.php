<?php
session_start();
require 'includes/db_connect.php';
require 'includes/auth_check.php';
require 'includes/functions.php';
require 'config.php';

$error = '';
$success = '';
$csrf_token = bin2hex(random_bytes(32));

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        $error = "Invalid form submission";
    } else {
        $type = sanitizeInput($_POST['emergency_type']);
        $description = sanitizeInput($_POST['description']);
        $location = sanitizeInput($_POST['location']);
        $latitude = floatval($_POST['latitude']);
        $longitude = floatval($_POST['longitude']);
        $contact = sanitizeInput($_POST['contact_number']);
        
        if (empty($type) || empty($description) || empty($location)) {
            $error = "All fields are required for emergency alerts";
        } else {
            try {
                // Insert emergency event
                $stmt = $pdo->prepare("
                    INSERT INTO events (user_id, title, description, type, severity_level, 
                                      latitude, longitude, location_description, created_at) 
                    VALUES (?, ?, ?, ?, 'critical', ?, ?, ?, NOW())
                ");
                $stmt->execute([
                    $_SESSION['user_id'],
                    "EMERGENCY: " . ucfirst($type),
                    $description,
                    $type,
                    $latitude,
                    $longitude,
                    $location
                ]);
                
                $eventId = $pdo->lastInsertId();
                
                // Send immediate notifications to all authorities
                $authorities = $pdo->query("
                    SELECT id, username, phone FROM users 
                    WHERE role IN ('police', 'medical', 'fire_dept', 'admin') 
                    AND is_active = TRUE
                ")->fetchAll();
                
                $message = "EMERGENCY ALERT: $type reported at $location. Contact: $contact";
                
                foreach ($authorities as $authority) {
                    // Database notification
                    $stmt = $pdo->prepare("
                        INSERT INTO notifications (user_id, message, type, created_at) 
                        VALUES (?, ?, 'danger', NOW())
                    ");
                    $stmt->execute([$authority['id'], $message]);
                    
                    // SMS notification (if configured)
                    if (defined('TWILIO_ENABLED') && TWILIO_ENABLED && $authority['phone']) {
                        sendSMS($authority['phone'], $message);
                    }
                }
                
                // Log emergency
                logActivity("EMERGENCY ALERT: $type at $location by " . $_SESSION['username']);
                
                $success = "Emergency alert sent successfully! Authorities have been notified.";
                
            } catch (PDOException $e) {
                $error = "Failed to send emergency alert. Please try again.";
                logError("Emergency alert error: " . $e->getMessage());
            }
        }
    }
}

$_SESSION['csrf_token'] = $csrf_token;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Alert | <?= APP_NAME ?></title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --primary-color: #2c3e50;
            --gradient-danger: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            --shadow-heavy: 0 20px 60px rgba(231, 76, 60, 0.3);
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            padding: 2rem 0;
            animation: urgentPulse 3s ease-in-out infinite;
        }
        
        @keyframes urgentPulse {
            0%, 100% { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); }
            50% { background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); }
        }
        
        .container {
            max-width: 600px;
        }
        
        .emergency-card {
            background: white;
            border-radius: 20px;
            box-shadow: var(--shadow-heavy);
            overflow: hidden;
            margin-bottom: 2rem;
            border: 3px solid var(--danger-color);
            animation: cardPulse 2s ease-in-out infinite;
        }
        
        @keyframes cardPulse {
            0%, 100% { transform: scale(1); box-shadow: var(--shadow-heavy); }
            50% { transform: scale(1.02); box-shadow: 0 25px 80px rgba(231, 76, 60, 0.4); }
        }
        
        .emergency-header {
            background: var(--gradient-danger);
            color: white;
            padding: 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .emergency-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255, 255, 255, 0.1) 10px,
                rgba(255, 255, 255, 0.1) 20px
            );
            animation: stripes 20s linear infinite;
        }
        
        @keyframes stripes {
            0% { transform: translateX(-100px); }
            100% { transform: translateX(100px); }
        }
        
        .emergency-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: iconBounce 1s ease-in-out infinite;
            position: relative;
            z-index: 1;
        }
        
        @keyframes iconBounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }
        
        .emergency-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 1;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .emergency-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .card-body {
            padding: 2rem;
        }
        
        .warning-banner {
            background: rgba(243, 156, 18, 0.1);
            border: 2px solid var(--warning-color);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .warning-text {
            color: var(--warning-color);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .form-section {
            margin-bottom: 2rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .form-label {
            font-weight: 500;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 0.875rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--danger-color);
            box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.15);
        }
        
        .emergency-type-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .emergency-type-card {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .emergency-type-card:hover {
            border-color: var(--danger-color);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.2);
        }
        
        .emergency-type-card.selected {
            border-color: var(--danger-color);
            background: rgba(231, 76, 60, 0.1);
        }
        
        .type-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            display: block;
            color: var(--danger-color);
        }
        
        .location-btn {
            background: var(--danger-color);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            margin-top: 1rem;
            transition: all 0.3s ease;
        }
        
        .location-btn:hover {
            background: #c0392b;
            transform: translateY(-1px);
        }
        
        .emergency-btn {
            background: var(--gradient-danger);
            border: none;
            border-radius: 12px;
            padding: 1rem 2rem;
            color: white;
            font-weight: 700;
            font-size: 1.2rem;
            width: 100%;
            margin-top: 2rem;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }
        
        .emergency-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .emergency-btn:hover::before {
            left: 100%;
        }
        
        .emergency-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(231, 76, 60, 0.4);
        }
        
        .emergency-btn:active {
            transform: translateY(0);
        }
        
        .back-btn {
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: #5a6268;
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
            animation: slideDown 0.5s ease-out;
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .alert-success {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
            border-left: 4px solid #27ae60;
        }
        
        .alert-danger {
            background: rgba(231, 76, 60, 0.1);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }
        
        .contact-info {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .contact-title {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.25rem;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 1rem 0;
            }
            
            .emergency-header {
                padding: 1.5rem;
            }
            
            .emergency-title {
                font-size: 2rem;
            }
            
            .emergency-icon {
                font-size: 3rem;
            }
            
            .card-body {
                padding: 1.5rem;
            }
            
            .emergency-type-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.php" class="back-btn">
            <i class="fas fa-arrow-left"></i>
            Back to Dashboard
        </a>
        
        <div class="emergency-card">
            <div class="emergency-header">
                <i class="emergency-icon fas fa-exclamation-triangle"></i>
                <h1 class="emergency-title">EMERGENCY ALERT</h1>
                <p class="emergency-subtitle">Send immediate alert to all emergency services</p>
            </div>
            
            <div class="card-body">
                <div class="warning-banner">
                    <div class="warning-text">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        EMERGENCY USE ONLY
                    </div>
                    <small>This will immediately notify police, medical, and fire departments. Use only for genuine emergencies.</small>
                </div>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?= htmlspecialchars($success) ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" id="emergencyForm">
                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                    <input type="hidden" name="latitude" id="latitude">
                    <input type="hidden" name="longitude" id="longitude">
                    
                    <!-- Emergency Type -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-exclamation-circle"></i>
                            Emergency Type
                        </h3>
                        
                        <input type="hidden" name="emergency_type" id="selectedType">
                        
                        <div class="emergency-type-grid">
                            <div class="emergency-type-card" data-type="accident" onclick="selectEmergencyType('accident')">
                                <i class="type-icon fas fa-car-crash"></i>
                                <div class="fw-bold">Accident</div>
                                <small>Traffic/Vehicle</small>
                            </div>
                            <div class="emergency-type-card" data-type="medical" onclick="selectEmergencyType('medical')">
                                <i class="type-icon fas fa-ambulance"></i>
                                <div class="fw-bold">Medical</div>
                                <small>Health Emergency</small>
                            </div>
                            <div class="emergency-type-card" data-type="fire" onclick="selectEmergencyType('fire')">
                                <i class="type-icon fas fa-fire"></i>
                                <div class="fw-bold">Fire</div>
                                <small>Fire/Explosion</small>
                            </div>
                            <div class="emergency-type-card" data-type="crime" onclick="selectEmergencyType('crime')">
                                <i class="type-icon fas fa-shield-alt"></i>
                                <div class="fw-bold">Crime</div>
                                <small>Security/Crime</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Description -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-comment-alt"></i>
                            Emergency Description
                        </h3>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">What is happening? *</label>
                            <textarea class="form-control" id="description" name="description" rows="4" 
                                      placeholder="Describe the emergency situation in detail..." required></textarea>
                        </div>
                    </div>
                    
                    <!-- Location -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-map-marker-alt"></i>
                            Location
                        </h3>
                        
                        <div class="mb-3">
                            <label for="location" class="form-label">Emergency Location *</label>
                            <input type="text" class="form-control" id="location" name="location" 
                                   placeholder="Exact location of the emergency" required>
                        </div>
                        
                        <button type="button" class="location-btn" onclick="getCurrentLocation()">
                            <i class="fas fa-location-arrow me-2"></i>
                            Use Current Location
                        </button>
                    </div>
                    
                    <!-- Contact -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-phone"></i>
                            Contact Information
                        </h3>
                        
                        <div class="mb-3">
                            <label for="contact_number" class="form-label">Contact Number *</label>
                            <input type="tel" class="form-control" id="contact_number" name="contact_number" 
                                   placeholder="Your phone number for emergency contact" required>
                        </div>
                        
                        <div class="contact-info">
                            <div class="contact-title">Emergency Services will contact you at:</div>
                            <div class="contact-item">
                                <i class="fas fa-phone text-primary"></i>
                                <span id="displayContact">Enter your number above</span>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="emergency-btn" id="emergencyBtn">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        SEND EMERGENCY ALERT
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedType = null;
        
        function selectEmergencyType(type) {
            selectedType = type;
            document.getElementById('selectedType').value = type;
            
            // Update UI
            document.querySelectorAll('.emergency-type-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-type="${type}"]`).classList.add('selected');
        }
        
        function getCurrentLocation() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Getting location...';
            btn.disabled = true;
            
            if ('geolocation' in navigator) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        const lat = position.coords.latitude;
                        const lng = position.coords.longitude;
                        
                        document.getElementById('latitude').value = lat;
                        document.getElementById('longitude').value = lng;
                        
                        // Reverse geocoding (simplified)
                        document.getElementById('location').value = `Current location (${lat.toFixed(4)}, ${lng.toFixed(4)})`;
                        
                        btn.innerHTML = '<i class="fas fa-check me-2"></i>Location obtained';
                        btn.style.background = '#27ae60';
                        
                        setTimeout(() => {
                            btn.innerHTML = originalText;
                            btn.style.background = '';
                            btn.disabled = false;
                        }, 3000);
                    },
                    (error) => {
                        console.error('Geolocation error:', error);
                        btn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Location failed';
                        btn.style.background = '#e74c3c';
                        
                        setTimeout(() => {
                            btn.innerHTML = originalText;
                            btn.style.background = '';
                            btn.disabled = false;
                        }, 3000);
                        
                        alert('Unable to get your location. Please enter it manually.');
                    }
                );
            } else {
                alert('Geolocation is not supported by this browser.');
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        }
        
        // Update contact display
        document.getElementById('contact_number').addEventListener('input', function() {
            const contact = this.value || 'Enter your number above';
            document.getElementById('displayContact').textContent = contact;
        });
        
        // Form validation
        document.getElementById('emergencyForm').addEventListener('submit', function(e) {
            if (!selectedType) {
                e.preventDefault();
                alert('Please select an emergency type');
                return;
            }
            
            const description = document.getElementById('description').value.trim();
            const location = document.getElementById('location').value.trim();
            const contact = document.getElementById('contact_number').value.trim();
            
            if (!description || !location || !contact) {
                e.preventDefault();
                alert('All fields are required for emergency alerts');
                return;
            }
            
            // Confirm emergency submission
            if (!confirm('Are you sure you want to send this emergency alert? This will immediately notify all emergency services.')) {
                e.preventDefault();
                return;
            }
            
            // Show loading state
            const btn = document.getElementById('emergencyBtn');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>SENDING ALERT...';
        });
        
        // Auto-get location on page load
        window.addEventListener('load', function() {
            if ('geolocation' in navigator) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        document.getElementById('latitude').value = position.coords.latitude;
                        document.getElementById('longitude').value = position.coords.longitude;
                    },
                    (error) => {
                        console.log('Background geolocation failed:', error);
                    }
                );
            }
        });
        
        // Prevent accidental page close
        window.addEventListener('beforeunload', function(e) {
            const form = document.getElementById('emergencyForm');
            const hasData = form.querySelector('textarea').value.trim() || 
                           form.querySelector('input[name="location"]').value.trim();
            
            if (hasData) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
    </script>
</body>
</html>
