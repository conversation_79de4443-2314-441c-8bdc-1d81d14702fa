<?php
/**
 * Simple Event Submission Test
 * Use this to test event submission without the complex form
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require 'includes/db_connect.php';
require 'includes/auth_check.php';
require 'includes/functions.php';
require 'config.php';

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = sanitizeInput($_POST['title'] ?? '');
    $description = sanitizeInput($_POST['description'] ?? '');
    $type = sanitizeInput($_POST['type'] ?? '');
    $severity = sanitizeInput($_POST['severity_level'] ?? '');
    $latitude = floatval($_POST['latitude'] ?? -1.9403);
    $longitude = floatval($_POST['longitude'] ?? 29.8739);
    $location_desc = sanitizeInput($_POST['location_description'] ?? '');

    echo "<h3>Debug Information:</h3>";
    echo "<pre>";
    echo "Title: " . var_export($title, true) . "\n";
    echo "Type: " . var_export($type, true) . "\n";
    echo "Severity: " . var_export($severity, true) . "\n";
    echo "Latitude: " . var_export($latitude, true) . "\n";
    echo "Longitude: " . var_export($longitude, true) . "\n";
    echo "Location Description: " . var_export($location_desc, true) . "\n";
    echo "User ID: " . var_export($_SESSION['user_id'] ?? 'NOT SET', true) . "\n";
    echo "</pre>";

    // Validate inputs
    if (empty($title)) {
        $error = "Event title is required";
    } elseif (empty($type)) {
        $error = "Event type is required";
    } elseif (empty($severity)) {
        $error = "Severity level is required";
    } elseif (!validateCoordinates($latitude, $longitude)) {
        $error = "Invalid coordinates (Lat: $latitude, Lng: $longitude)";
    } elseif (!isset($_SESSION['user_id'])) {
        $error = "User not logged in";
    } else {
        try {
            // Test database connection
            $testQuery = $pdo->query("SELECT 1");
            echo "<p style='color: green;'>✓ Database connection OK</p>";

            // Check if events table exists
            $tableCheck = $pdo->query("SHOW TABLES LIKE 'events'")->fetch();
            if (!$tableCheck) {
                $error = "Events table does not exist. Please run database setup.";
            } else {
                echo "<p style='color: green;'>✓ Events table exists</p>";

                // Insert event
                $stmt = $pdo->prepare("
                    INSERT INTO events (user_id, title, description, type, severity_level,
                                      latitude, longitude, location_description, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                
                $result = $stmt->execute([
                    $_SESSION['user_id'],
                    $title,
                    $description,
                    $type,
                    $severity,
                    $latitude,
                    $longitude,
                    $location_desc
                ]);

                if ($result) {
                    $eventId = $pdo->lastInsertId();
                    $success = "Event created successfully! Event ID: $eventId";
                    
                    // Log activity if function exists
                    if (function_exists('logActivity')) {
                        logActivity("Test event created: $title (ID: $eventId)");
                    }
                } else {
                    $error = "Failed to insert event";
                }
            }

        } catch (PDOException $e) {
            $error = "Database error: " . $e->getMessage();
            echo "<p style='color: red;'>Database Error Details: " . $e->getMessage() . "</p>";
        } catch (Exception $e) {
            $error = "General error: " . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Event Submission</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3>Test Event Submission</h3>
                        <p class="mb-0">Simple form to test event creation</p>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <strong>Error:</strong> <?= htmlspecialchars($error) ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <strong>Success:</strong> <?= htmlspecialchars($success) ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST">
                            <div class="mb-3">
                                <label for="title" class="form-label">Event Title *</label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       value="Test Event <?= date('Y-m-d H:i:s') ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3">This is a test event submission to verify the system is working.</textarea>
                            </div>

                            <div class="mb-3">
                                <label for="type" class="form-label">Event Type *</label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="">Select Type</option>
                                    <?php foreach ($EVENT_TYPES as $type => $config): ?>
                                        <option value="<?= $type ?>"><?= $config['name'] ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="severity_level" class="form-label">Severity Level *</label>
                                <select class="form-select" id="severity_level" name="severity_level" required>
                                    <option value="">Select Severity</option>
                                    <option value="low">Low</option>
                                    <option value="medium" selected>Medium</option>
                                    <option value="high">High</option>
                                    <option value="critical">Critical</option>
                                </select>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="latitude" class="form-label">Latitude</label>
                                        <input type="number" step="any" class="form-control" id="latitude" name="latitude" value="-1.9403">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="longitude" class="form-label">Longitude</label>
                                        <input type="number" step="any" class="form-control" id="longitude" name="longitude" value="29.8739">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="location_description" class="form-label">Location Description</label>
                                <input type="text" class="form-control" id="location_description" name="location_description" 
                                       value="Kigali City Center (Test Location)">
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>Submit Test Event
                            </button>
                            
                            <a href="index.php" class="btn btn-secondary ms-2">Back to Dashboard</a>
                        </form>

                        <hr class="my-4">
                        
                        <h5>System Status Check</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>User Logged In:</span>
                                        <span class="<?= isset($_SESSION['user_id']) ? 'text-success' : 'text-danger' ?>">
                                            <?= isset($_SESSION['user_id']) ? '✓ Yes (ID: ' . $_SESSION['user_id'] . ')' : '✗ No' ?>
                                        </span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Database Connected:</span>
                                        <span class="text-success">✓ Yes</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Config Loaded:</span>
                                        <span class="<?= defined('APP_NAME') ? 'text-success' : 'text-danger' ?>">
                                            <?= defined('APP_NAME') ? '✓ Yes' : '✗ No' ?>
                                        </span>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Event Types:</span>
                                        <span class="text-success">✓ <?= count($EVENT_TYPES) ?> types</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Functions Loaded:</span>
                                        <span class="<?= function_exists('validateCoordinates') ? 'text-success' : 'text-danger' ?>">
                                            <?= function_exists('validateCoordinates') ? '✓ Yes' : '✗ No' ?>
                                        </span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Session Active:</span>
                                        <span class="<?= session_status() === PHP_SESSION_ACTIVE ? 'text-success' : 'text-danger' ?>">
                                            <?= session_status() === PHP_SESSION_ACTIVE ? '✓ Yes' : '✗ No' ?>
                                        </span>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="mt-3">
                            <small class="text-muted">
                                <strong>Troubleshooting:</strong><br>
                                1. If you see errors, check <code>check_database.php</code><br>
                                2. Make sure you're logged in<br>
                                3. Verify database tables exist<br>
                                4. Check PHP error logs for details
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
