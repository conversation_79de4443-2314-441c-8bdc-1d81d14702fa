<?php
session_start();
require 'includes/db_connect.php';
require 'includes/functions.php';
require 'config.php';

// Redirect if already logged in
if (isset($_SESSION['user_id'])) {
    header("Location: index.php");
    exit;
}

// Organization codes
$orgCodes = [
    'POLICE123' => 'police',
    'MEDICAL456' => 'medical',
    'FIRE789' => 'fire_dept',
    'MEDIA456' => 'organization',
    'EVENTS789' => 'organization'
];

$error = '';
$success = '';
$csrf_token = bin2hex(random_bytes(32));

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        $error = "Invalid form submission";
        logActivity("CSRF token mismatch from IP: " . $_SERVER['REMOTE_ADDR']);
    } else {
        $username = sanitizeInput($_POST['username']);
        $email = sanitizeInput($_POST['email']);
        $password = $_POST['password'];
        $confirm = $_POST['confirm_password'];
        $orgCode = isset($_POST['org_code']) ? sanitizeInput($_POST['org_code']) : null;
        $phone = sanitizeInput($_POST['phone']);
        $terms = isset($_POST['terms']);

        // Validate inputs
        if (empty($username) || empty($email) || empty($password)) {
            $error = "Username, email and password are required";
        } elseif (!validateEmail($email)) {
            $error = "Please enter a valid email address";
        } elseif ($password !== $confirm) {
            $error = "Passwords do not match";
        } elseif (strlen($password) < 8) {
            $error = "Password must be at least 8 characters";
        } elseif (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/', $password)) {
            $error = "Password must contain at least one uppercase letter, one lowercase letter, and one number";
        } elseif ($phone && !validatePhone($phone)) {
            $error = "Please enter a valid phone number";
        } elseif (!$terms) {
            $error = "You must accept the terms and conditions";
        } else {
            try {
                // Check if username or email already exists
                $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
                $stmt->execute([$username, $email]);
                if ($stmt->fetch()) {
                    $error = "Username or email already exists";
                } else {
                    // Determine role
                    $role = 'user';
                    if ($orgCode) {
                        if (!isset($orgCodes[$orgCode])) {
                            $error = "Invalid organization code";
                        } else {
                            $role = $orgCodes[$orgCode];
                        }
                    }

                    if (!$error) {
                        $hashedPassword = hashPassword($password);

                        $stmt = $pdo->prepare("
                            INSERT INTO users (username, email, password, role, phone, is_verified, created_at)
                            VALUES (?, ?, ?, ?, ?, ?, NOW())
                        ");
                        $stmt->execute([
                            $username,
                            $email,
                            $hashedPassword,
                            $role,
                            $phone,
                            1 // Auto-verify for now
                        ]);

                        $userId = $pdo->lastInsertId();

                        // Log successful registration
                        logActivity("New user registered: $username ($email) with role: $role");

                        // Auto-login after registration
                        session_regenerate_id(true);
                        $_SESSION['user_id'] = $userId;
                        $_SESSION['role'] = $role;
                        $_SESSION['username'] = $username;
                        $_SESSION['last_activity'] = time();

                        header("Location: index.php?welcome=1");
                        exit;
                    }
                }
            } catch (PDOException $e) {
                $error = "Registration failed. Please try again.";
                logError("Registration error: " . $e->getMessage());
            }
        }
    }
}

// Generate new CSRF token for the form
$_SESSION['csrf_token'] = $csrf_token;
?>
<!DOCTYPE html>
<html>
<head>
    <title>Register | DOWN-RWANDA</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .org-section { display: none; }
    </style>
</head>
<body>
    <div class="container mt-5" style="max-width: 500px;">
        <div class="card shadow">
            <div class="card-body">
                <h2 class="text-center mb-4">Register</h2>

                <?php if ($error): ?>
                    <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
                <?php endif; ?>

                <form method="POST">
                    <div class="mb-3">
                        <label class="form-label">Username</label>
                        <input type="text" name="username" class="form-control" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Password</label>
                        <input type="password" name="password" class="form-control" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Confirm Password</label>
                        <input type="password" name="confirm_password" class="form-control" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Phone Number</label>
                        <input type="tel" name="phone" class="form-control" required>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" id="isOrganization" class="form-check-input">
                        <label class="form-check-label" for="isOrganization">Register as organization</label>
                    </div>

                    <div id="orgSection" class="org-section mb-3">
                        <label class="form-label">Organization Code</label>
                        <input type="text" name="org_code" class="form-control">
                        <small class="text-muted">Contact admin for organization codes</small>
                    </div>

                    <button type="submit" class="btn btn-primary w-100">Register</button>
                </form>

                <div class="mt-3 text-center">
                    Already have an account? <a href="login.php">Login here</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Toggle organization section
        document.getElementById('isOrganization').addEventListener('change', function() {
            document.getElementById('orgSection').style.display =
                this.checked ? 'block' : 'none';
        });
    </script>
</body>
</html>