<?php
session_start();
require 'includes/db_connect.php';

// Redirect if already logged in
if (isset($_SESSION['user_id'])) {
    header("Location: index.php");
    exit;
}

// Organization codes
$orgCodes = [
    'POLICE123' => 'police',
    'MEDIA456' => 'organization',
    'EVENTS789' => 'organization'
];

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username']);
    $password = $_POST['password'];
    $confirm = $_POST['confirm_password'];
    $orgCode = isset($_POST['org_code']) ? trim($_POST['org_code']) : null;
    $phone = preg_replace('/[^0-9]/', '', $_POST['phone']);

    // Validate inputs
    if (empty($username) || empty($password)) {
        $error = "Username and password are required";
    } elseif ($password !== $confirm) {
        $error = "Passwords do not match";
    } elseif (strlen($password) < 8) {
        $error = "Password must be at least 8 characters";
    } else {
        try {
            // Determine role
            $role = 'user';
            if ($orgCode) {
                if (!isset($orgCodes[$orgCode])) {
                    $error = "Invalid organization code";
                } else {
                    $role = $orgCodes[$orgCode];
                }
            }

            if (!$error) {
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, password, role, phone) 
                    VALUES (?, ?, ?, ?)
                ");
                $stmt->execute([
                    $username,
                    password_hash($password, PASSWORD_DEFAULT),
                    $role,
                    $phone
                ]);

                $_SESSION['user_id'] = $pdo->lastInsertId();
                $_SESSION['role'] = $role;
                $_SESSION['username'] = $username;
                header("Location: index.php");
                exit;
            }
        } catch (PDOException $e) {
            $error = "Username already exists";
        }
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Register | DOWN-RWANDA</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .org-section { display: none; }
    </style>
</head>
<body>
    <div class="container mt-5" style="max-width: 500px;">
        <div class="card shadow">
            <div class="card-body">
                <h2 class="text-center mb-4">Register</h2>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
                <?php endif; ?>

                <form method="POST">
                    <div class="mb-3">
                        <label class="form-label">Username</label>
                        <input type="text" name="username" class="form-control" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Password</label>
                        <input type="password" name="password" class="form-control" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Confirm Password</label>
                        <input type="password" name="confirm_password" class="form-control" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Phone Number</label>
                        <input type="tel" name="phone" class="form-control" required>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" id="isOrganization" class="form-check-input">
                        <label class="form-check-label" for="isOrganization">Register as organization</label>
                    </div>
                    
                    <div id="orgSection" class="org-section mb-3">
                        <label class="form-label">Organization Code</label>
                        <input type="text" name="org_code" class="form-control">
                        <small class="text-muted">Contact admin for organization codes</small>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">Register</button>
                </form>
                
                <div class="mt-3 text-center">
                    Already have an account? <a href="login.php">Login here</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Toggle organization section
        document.getElementById('isOrganization').addEventListener('change', function() {
            document.getElementById('orgSection').style.display = 
                this.checked ? 'block' : 'none';
        });
    </script>
</body>
</html>