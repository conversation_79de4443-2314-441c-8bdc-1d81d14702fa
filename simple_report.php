<?php
/**
 * Simple Event Report Form (No CSRF for testing)
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require 'includes/db_connect.php';
require 'includes/auth_check.php';
require 'includes/functions.php';
require 'config.php';

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = sanitizeInput($_POST['title'] ?? '');
    $description = sanitizeInput($_POST['description'] ?? '');
    $type = sanitizeInput($_POST['type'] ?? '');
    $severity = sanitizeInput($_POST['severity_level'] ?? '');
    $latitude = floatval($_POST['latitude'] ?? -1.9403);
    $longitude = floatval($_POST['longitude'] ?? 29.8739);
    $location_desc = sanitizeInput($_POST['location_description'] ?? '');

    // Validate inputs
    if (empty($title)) {
        $error = "Event title is required";
    } elseif (empty($type)) {
        $error = "Event type is required";
    } elseif (empty($severity)) {
        $error = "Severity level is required";
    } else {
        try {
            // Insert event
            $stmt = $pdo->prepare("
                INSERT INTO events (user_id, title, description, type, severity_level,
                                  latitude, longitude, location_description, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $result = $stmt->execute([
                $_SESSION['user_id'],
                $title,
                $description,
                $type,
                $severity,
                $latitude,
                $longitude,
                $location_desc
            ]);

            if ($result) {
                $eventId = $pdo->lastInsertId();
                $success = "Event created successfully! Event ID: $eventId";
                
                // Clear form
                $_POST = [];
            } else {
                $error = "Failed to insert event";
            }

        } catch (PDOException $e) {
            $error = "Database error: " . $e->getMessage();
        } catch (Exception $e) {
            $error = "Error: " . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Event Report</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Simple Event Report (No CSRF)
                        </h3>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?= htmlspecialchars($error) ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?= htmlspecialchars($success) ?>
                                <br><a href="index.php" class="btn btn-sm btn-outline-success mt-2">View Dashboard</a>
                            </div>
                        <?php endif; ?>

                        <form method="POST">
                            <div class="mb-3">
                                <label for="title" class="form-label">Event Title *</label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       value="<?= htmlspecialchars($_POST['title'] ?? '') ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3"><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="type" class="form-label">Event Type *</label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="">Select Type</option>
                                    <?php foreach ($EVENT_TYPES as $type => $config): ?>
                                        <option value="<?= $type ?>" <?= ($_POST['type'] ?? '') === $type ? 'selected' : '' ?>>
                                            <?= $config['name'] ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="severity_level" class="form-label">Severity Level *</label>
                                <select class="form-select" id="severity_level" name="severity_level" required>
                                    <option value="">Select Severity</option>
                                    <option value="low" <?= ($_POST['severity_level'] ?? '') === 'low' ? 'selected' : '' ?>>Low</option>
                                    <option value="medium" <?= ($_POST['severity_level'] ?? '') === 'medium' ? 'selected' : '' ?>>Medium</option>
                                    <option value="high" <?= ($_POST['severity_level'] ?? '') === 'high' ? 'selected' : '' ?>>High</option>
                                    <option value="critical" <?= ($_POST['severity_level'] ?? '') === 'critical' ? 'selected' : '' ?>>Critical</option>
                                </select>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="latitude" class="form-label">Latitude</label>
                                        <input type="number" step="any" class="form-control" id="latitude" name="latitude" 
                                               value="<?= $_POST['latitude'] ?? -1.9403 ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="longitude" class="form-label">Longitude</label>
                                        <input type="number" step="any" class="form-control" id="longitude" name="longitude" 
                                               value="<?= $_POST['longitude'] ?? 29.8739 ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="location_description" class="form-label">Location Description</label>
                                <input type="text" class="form-control" id="location_description" name="location_description" 
                                       value="<?= htmlspecialchars($_POST['location_description'] ?? '') ?>"
                                       placeholder="e.g., Near Kigali Convention Centre">
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>Submit Event Report
                                </button>
                                <a href="report_event.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Full Form
                                </a>
                            </div>
                        </form>

                        <hr class="my-4">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6>System Status:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>User: <?= $_SESSION['username'] ?? 'Unknown' ?></li>
                                    <li><i class="fas fa-check text-success me-2"></i>Database: Connected</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Session: Active</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Quick Links:</h6>
                                <ul class="list-unstyled">
                                    <li><a href="check_database.php"><i class="fas fa-database me-2"></i>Check Database</a></li>
                                    <li><a href="test_event_submission.php"><i class="fas fa-vial me-2"></i>Test Submission</a></li>
                                    <li><a href="index.php"><i class="fas fa-home me-2"></i>Dashboard</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
