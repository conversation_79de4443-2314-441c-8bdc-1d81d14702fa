<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require 'includes/db_connect.php';
require 'includes/auth_check.php';
require 'includes/functions.php';
require 'config.php';

$error = '';
$success = '';
$csrf_token = bin2hex(random_bytes(32));

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Debug: Log POST data
    error_log("POST data received: " . print_r($_POST, true));

    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        $error = "Invalid form submission";
    } else {
        $title = sanitizeInput($_POST['title'] ?? '');
        $description = sanitizeInput($_POST['description'] ?? '');
        $type = sanitizeInput($_POST['type'] ?? '');
        $severity = sanitizeInput($_POST['severity_level'] ?? '');
        $latitude = floatval($_POST['latitude'] ?? 0);
        $longitude = floatval($_POST['longitude'] ?? 0);
        $location_desc = sanitizeInput($_POST['location_description'] ?? '');

        // Debug: Log processed values
        error_log("Processed values - Title: $title, Type: $type, Severity: $severity, Lat: $latitude, Lng: $longitude");

        // Validate inputs
        if (empty($title)) {
            $error = "Event title is required";
        } elseif (empty($type)) {
            $error = "Event type is required";
        } elseif (empty($severity)) {
            $error = "Severity level is required";
        } elseif (!validateCoordinates($latitude, $longitude)) {
            // If coordinates are invalid, use default Kigali location
            if ($latitude == 0 && $longitude == 0) {
                $latitude = -1.9403;
                $longitude = 29.8739;
                $location_desc = $location_desc ?: "Kigali, Rwanda (Default Location)";
                error_log("Using default coordinates for event submission");
            } else {
                $error = "Please provide a valid location (Lat: $latitude, Lng: $longitude)";
            }
        }

        if (!$error) {
            try {
                // Insert event
                $stmt = $pdo->prepare("
                    INSERT INTO events (user_id, title, description, type, severity_level,
                                      latitude, longitude, location_description, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                $stmt->execute([
                    $_SESSION['user_id'],
                    $title,
                    $description,
                    $type,
                    $severity,
                    $latitude,
                    $longitude,
                    $location_desc
                ]);

                $eventId = $pdo->lastInsertId();

                // Handle file uploads
                if (!empty($_FILES['images']['name'][0])) {
                    $uploadDir = 'uploads/events/';
                    if (!is_dir($uploadDir)) {
                        mkdir($uploadDir, 0755, true);
                    }

                    foreach ($_FILES['images']['tmp_name'] as $key => $tmpName) {
                        if (!empty($tmpName)) {
                            $fileName = uniqid() . '_' . $_FILES['images']['name'][$key];
                            $filePath = $uploadDir . $fileName;

                            if (move_uploaded_file($tmpName, $filePath)) {
                                $stmt = $pdo->prepare("
                                    INSERT INTO event_images (event_id, image_path, uploaded_at)
                                    VALUES (?, ?, NOW())
                                ");
                                $stmt->execute([$eventId, $filePath]);
                            }
                        }
                    }
                }

                // Log activity
                logActivity("Event reported: $title (ID: $eventId) by " . $_SESSION['username']);

                // Send notifications for critical events
                if ($severity === 'critical') {
                    // Notify relevant authorities
                    $authorities = $pdo->query("
                        SELECT id FROM users
                        WHERE role IN ('police', 'medical', 'fire_dept', 'admin')
                    ")->fetchAll();

                    foreach ($authorities as $authority) {
                        $stmt = $pdo->prepare("
                            INSERT INTO notifications (user_id, message, type, created_at)
                            VALUES (?, ?, 'warning', NOW())
                        ");
                        $stmt->execute([
                            $authority['id'],
                            "Critical event reported: $title"
                        ]);
                    }
                }

                $success = "Event reported successfully!";

            } catch (PDOException $e) {
                $error = "Failed to submit event. Please try again. Error: " . $e->getMessage();
                if (function_exists('logError')) {
                    logError("Event submission error: " . $e->getMessage());
                } else {
                    error_log("Event submission error: " . $e->getMessage());
                }
            } catch (Exception $e) {
                $error = "Failed to submit event. Please try again. Error: " . $e->getMessage();
                if (function_exists('logError')) {
                    logError("Event submission error: " . $e->getMessage());
                } else {
                    error_log("Event submission error: " . $e->getMessage());
                }
            }
        }
    }
}

// Generate new CSRF token
$_SESSION['csrf_token'] = $csrf_token;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Report Event | <?= APP_NAME ?></title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/leaflet@1.9.4/dist/leaflet.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 5px 20px rgba(0, 0, 0, 0.15);
            --border-radius: 15px;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .container {
            max-width: 800px;
        }

        .report-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-medium);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .card-header {
            background: var(--gradient-primary);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .card-header h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .card-header p {
            opacity: 0.9;
            margin-bottom: 0;
        }

        .card-body {
            padding: 2rem;
        }

        .form-section {
            margin-bottom: 2rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #e9ecef;
        }

        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-label {
            font-weight: 500;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 0.875rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.15);
        }

        .event-type-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .event-type-card {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .event-type-card:hover {
            border-color: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow-light);
        }

        .event-type-card.selected {
            border-color: var(--primary-color);
            background: rgba(44, 62, 80, 0.1);
        }

        .event-type-card.emergency {
            border-color: var(--danger-color);
            background: rgba(231, 76, 60, 0.1);
        }

        .event-type-card.emergency.selected {
            border-color: var(--danger-color);
            background: rgba(231, 76, 60, 0.2);
        }

        .type-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .severity-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .severity-card {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .severity-card.low { border-color: var(--success-color); }
        .severity-card.medium { border-color: var(--secondary-color); }
        .severity-card.high { border-color: var(--warning-color); }
        .severity-card.critical { border-color: var(--danger-color); }

        .severity-card.selected {
            transform: scale(1.05);
            box-shadow: var(--shadow-medium);
        }

        .location-section {
            position: relative;
        }

        #map {
            height: 300px;
            border-radius: 12px;
            margin-top: 1rem;
        }

        .location-btn {
            background: var(--secondary-color);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            margin-top: 1rem;
            transition: all 0.3s ease;
        }

        .location-btn:hover {
            background: #2980b9;
            transform: translateY(-1px);
        }

        .image-upload-area {
            border: 2px dashed #ddd;
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .image-upload-area:hover {
            border-color: var(--secondary-color);
            background: rgba(52, 152, 219, 0.1);
        }

        .image-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 1rem;
        }

        .image-preview img {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 8px;
            border: 2px solid #ddd;
        }

        .submit-btn {
            background: var(--gradient-primary);
            border: none;
            border-radius: 12px;
            padding: 1rem 2rem;
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
            width: 100%;
            margin-top: 2rem;
            transition: all 0.3s ease;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .emergency-btn {
            background: linear-gradient(135deg, var(--danger-color), #c0392b);
        }

        .back-btn {
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #5a6268;
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
        }

        .alert-success {
            background: rgba(39, 174, 96, 0.1);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }

        .alert-danger {
            background: rgba(231, 76, 60, 0.1);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        @media (max-width: 768px) {
            body {
                padding: 1rem 0;
            }

            .card-header {
                padding: 1.5rem;
            }

            .card-body {
                padding: 1.5rem;
            }

            .event-type-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .severity-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.php" class="back-btn">
            <i class="fas fa-arrow-left"></i>
            Back to Dashboard
        </a>

        <div class="report-card">
            <div class="card-header">
                <h1><i class="fas fa-exclamation-triangle me-3"></i>Report Event</h1>
                <p>Help keep your community safe by reporting incidents and events</p>
            </div>

            <div class="card-body">
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?= htmlspecialchars($success) ?>
                    </div>
                <?php endif; ?>

                <form method="POST" enctype="multipart/form-data" id="eventForm">
                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                    <input type="hidden" name="latitude" id="latitude">
                    <input type="hidden" name="longitude" id="longitude">

                    <!-- Basic Information -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-info-circle"></i>
                            Basic Information
                        </h3>

                        <div class="mb-3">
                            <label for="title" class="form-label">Event Title *</label>
                            <input type="text" class="form-control" id="title" name="title"
                                   placeholder="Brief description of the event" required>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="4"
                                      placeholder="Provide more details about what happened..."></textarea>
                        </div>
                    </div>

                    <!-- Event Type -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-tags"></i>
                            Event Type
                        </h3>

                        <input type="hidden" name="type" id="selectedType">

                        <div class="event-type-grid">
                            <?php
                            global $EVENT_TYPES;
                            foreach ($EVENT_TYPES as $type => $config):
                            ?>
                            <div class="event-type-card <?= in_array($config['priority'], ['high', 'critical']) ? 'emergency' : '' ?>"
                                 data-type="<?= $type ?>" onclick="selectEventType('<?= $type ?>')">
                                <i class="type-icon <?= $config['icon'] ?>" style="color: <?= $config['color'] ?>"></i>
                                <div class="fw-bold"><?= $config['name'] ?></div>
                                <small class="text-muted"><?= ucfirst($config['priority']) ?></small>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Severity Level -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-exclamation-circle"></i>
                            Severity Level
                        </h3>

                        <input type="hidden" name="severity_level" id="selectedSeverity">

                        <div class="severity-grid">
                            <div class="severity-card low" data-severity="low" onclick="selectSeverity('low')">
                                <i class="fas fa-info-circle fa-2x text-success mb-2"></i>
                                <div class="fw-bold">Low</div>
                                <small>Minor incident</small>
                            </div>
                            <div class="severity-card medium" data-severity="medium" onclick="selectSeverity('medium')">
                                <i class="fas fa-exclamation-triangle fa-2x text-primary mb-2"></i>
                                <div class="fw-bold">Medium</div>
                                <small>Moderate incident</small>
                            </div>
                            <div class="severity-card high" data-severity="high" onclick="selectSeverity('high')">
                                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                                <div class="fw-bold">High</div>
                                <small>Serious incident</small>
                            </div>
                            <div class="severity-card critical" data-severity="critical" onclick="selectSeverity('critical')">
                                <i class="fas fa-exclamation-circle fa-2x text-danger mb-2"></i>
                                <div class="fw-bold">Critical</div>
                                <small>Emergency</small>
                            </div>
                        </div>
                    </div>

                    <!-- Location -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-map-marker-alt"></i>
                            Location
                        </h3>

                        <div class="mb-3">
                            <label for="location_description" class="form-label">Location Description</label>
                            <input type="text" class="form-control" id="location_description" name="location_description"
                                   placeholder="Describe the location (e.g., Near Kigali Convention Centre)">
                        </div>

                        <button type="button" class="location-btn" onclick="getCurrentLocation()">
                            <i class="fas fa-location-arrow me-2"></i>
                            Use Current Location
                        </button>

                        <div id="map"></div>
                        <small class="text-muted">Click on the map to set the exact location</small>
                    </div>

                    <!-- Images -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-camera"></i>
                            Images (Optional)
                        </h3>

                        <div class="image-upload-area" onclick="document.getElementById('imageInput').click()">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h5>Upload Images</h5>
                            <p class="text-muted mb-0">Click to select images or drag and drop</p>
                            <small class="text-muted">Maximum 5 images, 10MB each</small>
                        </div>

                        <input type="file" id="imageInput" name="images[]" multiple accept="image/*"
                               style="display: none;" onchange="previewImages(this)">

                        <div class="image-preview" id="imagePreview"></div>
                    </div>

                    <button type="submit" class="submit-btn" id="submitBtn">
                        <i class="fas fa-paper-plane me-2"></i>
                        Submit Event Report
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        let map;
        let marker;
        let selectedType = null;
        let selectedSeverity = null;

        // Initialize map
        function initMap() {
            map = L.map('map').setView([-1.9403, 29.8739], 13);

            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);

            // Add click handler
            map.on('click', function(e) {
                setLocation(e.latlng.lat, e.latlng.lng);
            });
        }

        function setLocation(lat, lng) {
            if (marker) {
                map.removeLayer(marker);
            }

            marker = L.marker([lat, lng]).addTo(map);
            document.getElementById('latitude').value = lat;
            document.getElementById('longitude').value = lng;
        }

        function getCurrentLocation() {
            if ('geolocation' in navigator) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        const lat = position.coords.latitude;
                        const lng = position.coords.longitude;

                        map.setView([lat, lng], 15);
                        setLocation(lat, lng);
                    },
                    (error) => {
                        alert('Unable to get your location. Please click on the map to set location.');
                    }
                );
            } else {
                alert('Geolocation is not supported by this browser.');
            }
        }

        function selectEventType(type) {
            selectedType = type;
            document.getElementById('selectedType').value = type;

            // Update UI
            document.querySelectorAll('.event-type-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-type="${type}"]`).classList.add('selected');
        }

        function selectSeverity(severity) {
            selectedSeverity = severity;
            document.getElementById('selectedSeverity').value = severity;

            // Update UI
            document.querySelectorAll('.severity-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-severity="${severity}"]`).classList.add('selected');

            // Update submit button for critical events
            const submitBtn = document.getElementById('submitBtn');
            if (severity === 'critical') {
                submitBtn.classList.add('emergency-btn');
                submitBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Submit Emergency Report';
            } else {
                submitBtn.classList.remove('emergency-btn');
                submitBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Submit Event Report';
            }
        }

        function previewImages(input) {
            const preview = document.getElementById('imagePreview');
            preview.innerHTML = '';

            if (input.files) {
                Array.from(input.files).slice(0, 5).forEach((file, index) => {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        preview.appendChild(img);
                    };
                    reader.readAsDataURL(file);
                });
            }
        }

        // Form validation
        document.getElementById('eventForm').addEventListener('submit', function(e) {
            if (!selectedType) {
                e.preventDefault();
                alert('Please select an event type');
                return;
            }

            if (!selectedSeverity) {
                e.preventDefault();
                alert('Please select a severity level');
                return;
            }

            const lat = document.getElementById('latitude').value;
            const lng = document.getElementById('longitude').value;

            if (!lat || !lng) {
                e.preventDefault();
                alert('Please set the event location on the map');
                return;
            }

            // Show loading state
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Submitting...';
        });

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initMap();

            // Set default coordinates (Kigali city center)
            document.getElementById('latitude').value = -1.9403;
            document.getElementById('longitude').value = 29.8739;

            // Add default marker
            setLocation(-1.9403, 29.8739);

            // Try to get user's location automatically
            setTimeout(getCurrentLocation, 1000);
        });
    </script>
</body>
</html>
