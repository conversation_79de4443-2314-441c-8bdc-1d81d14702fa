-- DOWN-RWANDA Database Schema
-- Run this SQL to create the required database structure

-- Create database (uncomment if needed)
-- CREATE DATABASE down_rwanda CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE down_rwanda;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('user', 'police', 'medical', 'fire_dept', 'organization', 'admin') DEFAULT 'user',
    phone VARCHAR(20),
    avatar VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    email_verified_at TIMESTAMP NULL,
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIM<PERSON><PERSON><PERSON> NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role)
);

-- Events table
CREATE TABLE IF NOT EXISTS events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    type ENUM('accident', 'fire', 'medical', 'crime', 'show', 'party', 'sports') NOT NULL,
    severity_level ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    location_description VARCHAR(255),
    is_approved BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    payment_status ENUM('pending', 'paid', 'not_required') DEFAULT 'not_required',
    payment_proof VARCHAR(255),
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_severity (severity_level),
    INDEX idx_location (latitude, longitude),
    INDEX idx_created_at (created_at),
    INDEX idx_approved (is_approved)
);

-- Event images table
CREATE TABLE IF NOT EXISTS event_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_id INT NOT NULL,
    image_path VARCHAR(255) NOT NULL,
    image_size INT,
    image_type VARCHAR(50),
    is_primary BOOLEAN DEFAULT FALSE,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    INDEX idx_event_id (event_id)
);

-- Event analytics table
CREATE TABLE IF NOT EXISTS event_analytics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_id INT NOT NULL,
    views INT DEFAULT 0,
    shares INT DEFAULT 0,
    reports INT DEFAULT 0,
    last_viewed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    UNIQUE KEY unique_event_analytics (event_id)
);

-- Notifications table
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255),
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'danger') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    action_url VARCHAR(255),
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
);

-- Police units table
CREATE TABLE IF NOT EXISTS police_units (
    id INT AUTO_INCREMENT PRIMARY KEY,
    unit_name VARCHAR(100) NOT NULL,
    unit_code VARCHAR(20) UNIQUE NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    status ENUM('available', 'busy', 'offline') DEFAULT 'available',
    contact_number VARCHAR(20),
    assigned_area VARCHAR(255),
    last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_location (latitude, longitude)
);

-- Emergency responses table
CREATE TABLE IF NOT EXISTS emergency_responses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_id INT NOT NULL,
    responder_type ENUM('police', 'medical', 'fire_dept') NOT NULL,
    unit_id INT,
    status ENUM('dispatched', 'en_route', 'on_scene', 'completed', 'cancelled') DEFAULT 'dispatched',
    response_time INT, -- in minutes
    arrival_time TIMESTAMP NULL,
    completion_time TIMESTAMP NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    INDEX idx_event_id (event_id),
    INDEX idx_responder_type (responder_type),
    INDEX idx_status (status)
);

-- User sessions table
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    payload TEXT,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_last_activity (last_activity)
);

-- API keys table
CREATE TABLE IF NOT EXISTS api_keys (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    key_name VARCHAR(100) NOT NULL,
    api_key VARCHAR(255) UNIQUE NOT NULL,
    permissions JSON,
    rate_limit INT DEFAULT 1000,
    is_active BOOLEAN DEFAULT TRUE,
    last_used TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_api_key (api_key),
    INDEX idx_user_id (user_id)
);

-- External events table (for imported events)
CREATE TABLE IF NOT EXISTS external_events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    source VARCHAR(100) NOT NULL,
    external_id VARCHAR(255),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    event_date TIMESTAMP,
    location VARCHAR(255),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    category VARCHAR(100),
    url VARCHAR(500),
    image_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    imported_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_source (source),
    INDEX idx_event_date (event_date),
    INDEX idx_location (latitude, longitude)
);

-- Archived events table
CREATE TABLE IF NOT EXISTS archived_events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    original_event_id INT NOT NULL,
    event_data JSON NOT NULL,
    archived_reason VARCHAR(255),
    archived_by INT,
    archived_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_original_event_id (original_event_id),
    INDEX idx_archived_at (archived_at)
);

-- System logs table
CREATE TABLE IF NOT EXISTS system_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') NOT NULL,
    message TEXT NOT NULL,
    context JSON,
    user_id INT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_level (level),
    INDEX idx_created_at (created_at),
    INDEX idx_user_id (user_id)
);

-- Insert default admin user (password: admin123)
INSERT IGNORE INTO users (username, email, password, role, is_verified) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', TRUE);

-- Insert sample police units
INSERT IGNORE INTO police_units (unit_name, unit_code, latitude, longitude, contact_number, assigned_area) VALUES 
('Kigali Central Police', 'KCP001', -1.9403, 29.8739, '+250788123456', 'Kigali City Center'),
('Nyarugenge Police', 'NYR001', -1.9536, 29.8739, '+250788123457', 'Nyarugenge District'),
('Gasabo Police', 'GSB001', -1.9200, 29.8800, '+250788123458', 'Gasabo District'),
('Kicukiro Police', 'KCK001', -1.9700, 29.8600, '+250788123459', 'Kicukiro District');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_events_location_type ON events(latitude, longitude, type);
CREATE INDEX IF NOT EXISTS idx_events_severity_created ON events(severity_level, created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_user_unread ON notifications(user_id, is_read, created_at);

-- Create views for common queries
CREATE OR REPLACE VIEW active_events AS
SELECT 
    e.*,
    u.username,
    u.role as user_role,
    ea.views,
    ea.shares,
    (SELECT COUNT(*) FROM event_images ei WHERE ei.event_id = e.id) as image_count
FROM events e
LEFT JOIN users u ON e.user_id = u.id
LEFT JOIN event_analytics ea ON e.id = ea.event_id
WHERE e.is_active = TRUE AND e.is_approved = TRUE;

CREATE OR REPLACE VIEW emergency_events AS
SELECT 
    e.*,
    u.username,
    u.phone as user_phone,
    ea.views
FROM events e
LEFT JOIN users u ON e.user_id = u.id
LEFT JOIN event_analytics ea ON e.id = ea.event_id
WHERE e.severity_level IN ('high', 'critical') 
AND e.is_active = TRUE 
AND e.is_approved = TRUE
ORDER BY e.created_at DESC;

-- Triggers for automatic analytics
DELIMITER //

CREATE TRIGGER IF NOT EXISTS create_event_analytics 
AFTER INSERT ON events
FOR EACH ROW
BEGIN
    INSERT INTO event_analytics (event_id) VALUES (NEW.id);
END//

CREATE TRIGGER IF NOT EXISTS update_event_timestamp
BEFORE UPDATE ON events
FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END//

DELIMITER ;

-- Grant permissions (adjust as needed)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON down_rwanda.* TO 'webapp'@'localhost';
-- FLUSH PRIVILEGES;
