<?php
/**
 * DOWN-RWANDA System Test Suite
 * Comprehensive testing of all system components
 */

require_once 'includes/db_connect.php';
require_once 'includes/functions.php';
require_once 'config.php';

class SystemTester {
    private $pdo;
    private $results = [];
    private $testUser = null;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    public function runAllTests() {
        echo "<h1>DOWN-RWANDA System Test Suite</h1>\n";
        echo "<style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
            .pass { color: green; font-weight: bold; }
            .fail { color: red; font-weight: bold; }
            .info { color: blue; }
            .warning { color: orange; }
            pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        </style>\n";
        
        $this->testDatabaseConnection();
        $this->testDatabaseSchema();
        $this->testUserFunctions();
        $this->testEventFunctions();
        $this->testAPIEndpoints();
        $this->testFileOperations();
        $this->testSecurityFeatures();
        $this->testMobileComponents();
        $this->testAnalytics();
        
        $this->displaySummary();
    }
    
    private function testDatabaseConnection() {
        echo "<div class='test-section'>\n";
        echo "<h2>Database Connection Tests</h2>\n";
        
        try {
            $this->pdo->query("SELECT 1");
            $this->pass("Database connection successful");
            
            // Test database version
            $version = $this->pdo->query("SELECT VERSION()")->fetchColumn();
            $this->info("MySQL Version: " . $version);
            
            // Test character set
            $charset = $this->pdo->query("SELECT @@character_set_database")->fetchColumn();
            $this->info("Character Set: " . $charset);
            
        } catch (PDOException $e) {
            $this->fail("Database connection failed: " . $e->getMessage());
        }
        
        echo "</div>\n";
    }
    
    private function testDatabaseSchema() {
        echo "<div class='test-section'>\n";
        echo "<h2>Database Schema Tests</h2>\n";
        
        $requiredTables = [
            'users', 'events', 'event_analytics', 'event_images', 
            'notifications', 'police_units', 'emergency_responses',
            'user_sessions', 'api_keys', 'archived_events'
        ];
        
        foreach ($requiredTables as $table) {
            try {
                $result = $this->pdo->query("SHOW TABLES LIKE '$table'")->fetch();
                if ($result) {
                    $this->pass("Table '$table' exists");
                    
                    // Check table structure
                    $columns = $this->pdo->query("DESCRIBE $table")->fetchAll();
                    $this->info("Table '$table' has " . count($columns) . " columns");
                } else {
                    $this->fail("Table '$table' missing");
                }
            } catch (PDOException $e) {
                $this->fail("Error checking table '$table': " . $e->getMessage());
            }
        }
        
        echo "</div>\n";
    }
    
    private function testUserFunctions() {
        echo "<div class='test-section'>\n";
        echo "<h2>User Function Tests</h2>\n";
        
        // Test password hashing
        $password = "testpassword123";
        $hash = hashPassword($password);
        if (verifyPassword($password, $hash)) {
            $this->pass("Password hashing and verification works");
        } else {
            $this->fail("Password hashing/verification failed");
        }
        
        // Test email validation
        if (validateEmail("<EMAIL>")) {
            $this->pass("Email validation works for valid email");
        } else {
            $this->fail("Email validation failed for valid email");
        }
        
        if (!validateEmail("invalid-email")) {
            $this->pass("Email validation rejects invalid email");
        } else {
            $this->fail("Email validation accepts invalid email");
        }
        
        // Test phone validation
        if (validatePhone("+250788123456")) {
            $this->pass("Phone validation works for valid phone");
        } else {
            $this->fail("Phone validation failed for valid phone");
        }
        
        // Test coordinate validation
        if (validateCoordinates(-1.9403, 29.8739)) {
            $this->pass("Coordinate validation works for valid coordinates");
        } else {
            $this->fail("Coordinate validation failed for valid coordinates");
        }
        
        echo "</div>\n";
    }
    
    private function testEventFunctions() {
        echo "<div class='test-section'>\n";
        echo "<h2>Event Function Tests</h2>\n";
        
        // Test event type configuration
        global $EVENT_TYPES;
        if (!empty($EVENT_TYPES)) {
            $this->pass("Event types configuration loaded (" . count($EVENT_TYPES) . " types)");
            
            foreach ($EVENT_TYPES as $type => $config) {
                if (isset($config['name'], $config['priority'], $config['icon'])) {
                    $this->pass("Event type '$type' properly configured");
                } else {
                    $this->fail("Event type '$type' missing required configuration");
                }
            }
        } else {
            $this->fail("Event types configuration not loaded");
        }
        
        // Test distance calculation
        $distance = getDistance(-1.9403, 29.8739, -1.9500, 29.8800);
        if ($distance > 0 && $distance < 10) {
            $this->pass("Distance calculation works (calculated: " . round($distance, 2) . "km)");
        } else {
            $this->fail("Distance calculation failed or returned unexpected value: " . $distance);
        }
        
        echo "</div>\n";
    }
    
    private function testAPIEndpoints() {
        echo "<div class='test-section'>\n";
        echo "<h2>API Endpoint Tests</h2>\n";
        
        $baseUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/api/v2";
        
        // Test health endpoint
        $healthResponse = $this->makeAPIRequest($baseUrl . "/health");
        if ($healthResponse && isset($healthResponse['status'])) {
            $this->pass("Health endpoint responds correctly");
            $this->info("System status: " . $healthResponse['status']);
        } else {
            $this->fail("Health endpoint not responding");
        }
        
        // Test auth endpoint structure
        $authResponse = $this->makeAPIRequest($baseUrl . "/auth", 'POST', [
            'action' => 'login',
            'username' => 'nonexistent',
            'password' => 'test'
        ]);
        
        if ($authResponse && isset($authResponse['error'])) {
            $this->pass("Auth endpoint responds to invalid credentials correctly");
        } else {
            $this->fail("Auth endpoint not responding correctly");
        }
        
        echo "</div>\n";
    }
    
    private function testFileOperations() {
        echo "<div class='test-section'>\n";
        echo "<h2>File Operation Tests</h2>\n";
        
        // Test upload directories
        $uploadDirs = ['uploads', 'uploads/events', 'uploads/avatars', 'uploads/evidence', 'uploads/payments'];
        
        foreach ($uploadDirs as $dir) {
            if (is_dir($dir)) {
                if (is_writable($dir)) {
                    $this->pass("Directory '$dir' exists and is writable");
                } else {
                    $this->warning("Directory '$dir' exists but is not writable");
                }
            } else {
                $this->fail("Directory '$dir' does not exist");
            }
        }
        
        // Test cache directory
        if (is_dir('cache') && is_writable('cache')) {
            $this->pass("Cache directory exists and is writable");
        } else {
            $this->warning("Cache directory not properly configured");
        }
        
        // Test logs directory
        if (is_dir('logs') && is_writable('logs')) {
            $this->pass("Logs directory exists and is writable");
        } else {
            $this->warning("Logs directory not properly configured");
        }
        
        echo "</div>\n";
    }
    
    private function testSecurityFeatures() {
        echo "<div class='test-section'>\n";
        echo "<h2>Security Feature Tests</h2>\n";
        
        // Test JWT functions
        if (function_exists('generateJWT') && function_exists('verifyJWT')) {
            $payload = ['user_id' => 1, 'username' => 'test', 'exp' => time() + 3600];
            $token = generateJWT($payload);
            $verified = verifyJWT($token);
            
            if ($verified && $verified['user_id'] == 1) {
                $this->pass("JWT generation and verification works");
            } else {
                $this->fail("JWT functions not working correctly");
            }
        } else {
            $this->fail("JWT functions not available");
        }
        
        // Test input sanitization
        $dirtyInput = "<script>alert('xss')</script>";
        $cleanInput = sanitizeInput($dirtyInput);
        if ($cleanInput !== $dirtyInput && !strpos($cleanInput, '<script>')) {
            $this->pass("Input sanitization works");
        } else {
            $this->fail("Input sanitization not working");
        }
        
        // Test rate limiting configuration
        if (defined('RATE_LIMIT_ENABLED')) {
            $this->pass("Rate limiting configuration defined");
        } else {
            $this->warning("Rate limiting configuration not defined");
        }
        
        echo "</div>\n";
    }
    
    private function testMobileComponents() {
        echo "<div class='test-section'>\n";
        echo "<h2>Mobile Component Tests</h2>\n";
        
        $mobileFiles = [
            'mobile/index.php',
            'mobile/report.php',
            'mobile/map.php',
            'mobile/profile.php',
            'mobile/notifications.php',
            'mobile/manifest.json',
            'mobile/sw.js',
            'mobile/mobile-app.js'
        ];
        
        foreach ($mobileFiles as $file) {
            if (file_exists($file)) {
                $this->pass("Mobile file '$file' exists");
            } else {
                $this->fail("Mobile file '$file' missing");
            }
        }
        
        // Test PWA manifest
        if (file_exists('mobile/manifest.json')) {
            $manifest = json_decode(file_get_contents('mobile/manifest.json'), true);
            if ($manifest && isset($manifest['name'], $manifest['start_url'])) {
                $this->pass("PWA manifest is valid");
            } else {
                $this->fail("PWA manifest is invalid");
            }
        }
        
        echo "</div>\n";
    }
    
    private function testAnalytics() {
        echo "<div class='test-section'>\n";
        echo "<h2>Analytics Component Tests</h2>\n";
        
        if (file_exists('analytics/advanced_analytics.php')) {
            $this->pass("Advanced analytics component exists");
            
            // Test analytics class
            require_once 'analytics/advanced_analytics.php';
            if (class_exists('AdvancedAnalytics')) {
                $this->pass("AdvancedAnalytics class available");
            } else {
                $this->fail("AdvancedAnalytics class not found");
            }
        } else {
            $this->fail("Advanced analytics component missing");
        }
        
        if (file_exists('dashboard/advanced.php')) {
            $this->pass("Advanced dashboard exists");
        } else {
            $this->fail("Advanced dashboard missing");
        }
        
        echo "</div>\n";
    }
    
    private function makeAPIRequest($url, $method = 'GET', $data = null) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return $response ? json_decode($response, true) : null;
    }
    
    private function pass($message) {
        echo "<div class='pass'>✓ PASS: $message</div>\n";
        $this->results['pass']++;
    }
    
    private function fail($message) {
        echo "<div class='fail'>✗ FAIL: $message</div>\n";
        $this->results['fail']++;
    }
    
    private function warning($message) {
        echo "<div class='warning'>⚠ WARNING: $message</div>\n";
        $this->results['warning']++;
    }
    
    private function info($message) {
        echo "<div class='info'>ℹ INFO: $message</div>\n";
    }
    
    private function displaySummary() {
        $total = ($this->results['pass'] ?? 0) + ($this->results['fail'] ?? 0) + ($this->results['warning'] ?? 0);
        
        echo "<div class='test-section'>\n";
        echo "<h2>Test Summary</h2>\n";
        echo "<p><strong>Total Tests:</strong> $total</p>\n";
        echo "<p class='pass'><strong>Passed:</strong> " . ($this->results['pass'] ?? 0) . "</p>\n";
        echo "<p class='fail'><strong>Failed:</strong> " . ($this->results['fail'] ?? 0) . "</p>\n";
        echo "<p class='warning'><strong>Warnings:</strong> " . ($this->results['warning'] ?? 0) . "</p>\n";
        
        $successRate = $total > 0 ? round((($this->results['pass'] ?? 0) / $total) * 100, 1) : 0;
        echo "<p><strong>Success Rate:</strong> $successRate%</p>\n";
        
        if (($this->results['fail'] ?? 0) == 0) {
            echo "<div class='pass'><h3>🎉 All critical tests passed! System is ready for deployment.</h3></div>\n";
        } else {
            echo "<div class='fail'><h3>❌ Some tests failed. Please review and fix issues before deployment.</h3></div>\n";
        }
        
        echo "</div>\n";
    }
}

// Initialize results array
$tester = new SystemTester($pdo);
$tester->results = ['pass' => 0, 'fail' => 0, 'warning' => 0];

// Run all tests
$tester->runAllTests();
?>
