# DOWN-RWANDA Environment Configuration
# Copy this file to .env and update with your actual values

# Application Settings
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost/DOWN-RWANDA

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=down_rwanda
DB_USERNAME=root
DB_PASSWORD=

# Security Settings
JWT_SECRET=your-super-secret-jwt-key-change-in-production-min-32-chars
SESSION_SECRET=your-session-secret-key-change-in-production
ENCRYPTION_KEY=your-encryption-key-32-characters-long

# API Keys and External Services
GOOGLE_MAPS_API_KEY=your-google-maps********
WEATHER_API_KEY=your-openweather********
TRANSLATION_API_KEY=your-google-translate********

# SMS/Communication Services
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# Alternative SMS providers
NEXMO_API_KEY=your-nexmo********
NEXMO_API_SECRET=your-nexmo-api-secret

# Email Configuration
MAIL_DRIVER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="DOWN-RWANDA System"

# Push Notifications
FIREBASE_SERVER_KEY=your-firebase-server-key
FIREBASE_PROJECT_ID=your-firebase-project-id
VAPID_PUBLIC_KEY=your-vapid-public-key
VAPID_PRIVATE_KEY=your-vapid-private-key

# Cloud Storage (Optional)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=down-rwanda-storage

# Google Cloud (Optional)
GOOGLE_CLOUD_PROJECT_ID=your-gcp-project-id
GOOGLE_CLOUD_KEY_FILE=path/to/service-account.json

# Analytics and Monitoring
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
SENTRY_DSN=your-sentry-dsn-for-error-tracking

# Social Media Integration
TWITTER_API_KEY=your-twitter********
TWITTER_API_SECRET=your-twitter-api-secret
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret

# Payment Processing (if needed)
STRIPE_PUBLIC_KEY=pk_test_your-stripe-public-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret

# AI/ML Services
OPENAI_API_KEY=your-openai********
GOOGLE_VISION_API_KEY=your-google-vision********
AZURE_COGNITIVE_KEY=your-azure-cognitive-services-key

# Caching Configuration
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0

MEMCACHED_HOST=127.0.0.1
MEMCACHED_PORT=11211

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_HOUR=100
RATE_LIMIT_BURST_LIMIT=20

# Security Settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-API-Key

# File Upload Settings
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf
UPLOAD_PATH=uploads/

# Logging Configuration
LOG_LEVEL=INFO
LOG_CHANNEL=file
LOG_MAX_FILES=30

# Performance Settings
CACHE_ENABLED=true
CACHE_DRIVER=file
CACHE_TTL=300

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=daily
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE=local

# Monitoring and Health Checks
HEALTH_CHECK_ENABLED=true
MONITORING_ENABLED=true
UPTIME_CHECK_URL=https://your-monitoring-service.com/webhook

# Development Settings (only for development)
DEV_TOOLBAR_ENABLED=true
DEV_PROFILER_ENABLED=true
DEV_MOCK_SMS=true
DEV_MOCK_EMAIL=true

# Testing Configuration
TEST_DATABASE=down_rwanda_test
TEST_REDIS_DATABASE=1

# Webhook URLs
WEBHOOK_EMERGENCY_ALERT=https://your-webhook-url.com/emergency
WEBHOOK_EVENT_CREATED=https://your-webhook-url.com/event-created
WEBHOOK_USER_REGISTERED=https://your-webhook-url.com/user-registered

# Third-party Integrations
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/YOUR/DISCORD/WEBHOOK

# Geolocation Services
MAPBOX_ACCESS_TOKEN=your-mapbox-access-token
HERE_API_KEY=your-here-maps********

# Emergency Services Integration
POLICE_API_ENDPOINT=https://police-system.gov.rw/api
HOSPITAL_API_ENDPOINT=https://hospital-system.gov.rw/api
FIRE_DEPT_API_ENDPOINT=https://fire-dept.gov.rw/api

# Government Integration
GOV_API_KEY=your-government********
GOV_API_ENDPOINT=https://api.gov.rw

# Mobile App Configuration
MOBILE_APP_VERSION=2.0.0
MOBILE_FORCE_UPDATE=false
MOBILE_MAINTENANCE_MODE=false

# Feature Flags
FEATURE_AI_ANALYSIS=true
FEATURE_PREDICTIVE_ANALYTICS=true
FEATURE_REAL_TIME_TRACKING=true
FEATURE_SOCIAL_SHARING=true
FEATURE_OFFLINE_MODE=true
FEATURE_VOICE_COMMANDS=false
FEATURE_FACIAL_RECOGNITION=false

# Compliance and Legal
GDPR_COMPLIANCE=true
DATA_RETENTION_DAYS=365
PRIVACY_POLICY_URL=https://downrwanda.com/privacy
TERMS_OF_SERVICE_URL=https://downrwanda.com/terms

# Localization
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,rw,fr,sw
TIMEZONE=Africa/Kigali

# Performance Monitoring
NEW_RELIC_LICENSE_KEY=your-new-relic-license-key
DATADOG_API_KEY=your-datadog********

# Content Delivery Network
CDN_URL=https://cdn.downrwanda.com
CDN_ENABLED=false

# Search Engine
ELASTICSEARCH_HOST=localhost:9200
ELASTICSEARCH_INDEX=down_rwanda

# Queue Configuration
QUEUE_DRIVER=database
QUEUE_CONNECTION=default

# Broadcasting
BROADCAST_DRIVER=pusher
PUSHER_APP_ID=your-pusher-app-id
PUSHER_APP_KEY=your-pusher-app-key
PUSHER_APP_SECRET=your-pusher-app-secret
PUSHER_APP_CLUSTER=mt1

# Session Configuration
SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=
SESSION_SECURE_COOKIE=false

# Additional Security
TRUSTED_PROXIES=
TRUSTED_HOSTS=^localhost|downrwanda\.com$

# API Documentation
API_DOCS_ENABLED=true
API_DOCS_URL=/api/docs

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="System is under maintenance. Please try again later."
MAINTENANCE_ALLOWED_IPS=127.0.0.1

# Custom Settings
ORGANIZATION_NAME="DOWN-RWANDA Emergency Response"
SUPPORT_EMAIL=<EMAIL>
SUPPORT_PHONE=+250788123456
EMERGENCY_CONTACT=+250788911911

# Version Information
VERSION=2.0.0
BUILD_NUMBER=
RELEASE_DATE=2024-01-15
