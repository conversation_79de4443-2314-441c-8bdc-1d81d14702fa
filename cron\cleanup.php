<?php
require '../includes/db_connect.php';
require '../includes/functions.php';

// Archive old events (older than 30 days)
$pdo->exec("
    INSERT INTO archived_events 
    SELECT * FROM events 
    WHERE created_at < NOW() - INTERVAL 30 DAY
");

$pdo->exec("
    DELETE FROM events 
    WHERE created_at < NOW() - INTERVAL 30 DAY
");

// Cleanup unused files
$usedFiles = $pdo->query("
    SELECT payment_proof FROM events 
    UNION SELECT avatar FROM users
")->fetchAll(PDO::FETCH_COLUMN);

$uploadDir = UPLOAD_DIR;
foreach (glob($uploadDir . "*") as $file) {
    if (!in_array(basename($file), $usedFiles) && 
        filemtime($file) < time() - 86400) {
        unlink($file);
    }
}

logActivity("Cron: Cleanup completed successfully");
?>