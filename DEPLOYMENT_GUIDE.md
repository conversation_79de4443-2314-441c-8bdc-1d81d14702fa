# DOWN-RWANDA v2.0 Deployment Guide

## 🚀 Quick Start Deployment

### Prerequisites
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **PHP**: 8.0 or higher with extensions:
  - PDO MySQL
  - GD or ImageMagick
  - cURL
  - JSON
  - OpenSSL
  - mbstring
- **MySQL**: 8.0 or higher
- **SSL Certificate**: Required for production
- **Domain**: Configured and pointing to server

### Step 1: Server Setup

#### Apache Configuration
```apache
<VirtualHost *:443>
    ServerName downrwanda.com
    DocumentRoot /var/www/down-rwanda
    
    # SSL Configuration
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    
    # Security Headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    
    # API Rewrite Rules
    RewriteEngine On
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^api/v2/(.*)$ api/v2/index.php [QSA,L]
    
    # Mobile PWA Rules
    RewriteRule ^mobile/(.*)$ mobile/index.php [QSA,L]
    
    # Security - Block access to sensitive files
    <FilesMatch "\.(env|log|sql|md)$">
        Require all denied
    </FilesMatch>
    
    # Enable compression
    LoadModule deflate_module modules/mod_deflate.so
    <Location />
        SetOutputFilter DEFLATE
        SetEnvIfNoCase Request_URI \
            \.(?:gif|jpe?g|png)$ no-gzip dont-vary
        SetEnvIfNoCase Request_URI \
            \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
    </Location>
</VirtualHost>

# Redirect HTTP to HTTPS
<VirtualHost *:80>
    ServerName downrwanda.com
    Redirect permanent / https://downrwanda.com/
</VirtualHost>
```

#### Nginx Configuration
```nginx
server {
    listen 443 ssl http2;
    server_name downrwanda.com;
    root /var/www/down-rwanda;
    index index.php;
    
    # SSL Configuration
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    # Security Headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    # API Routes
    location /api/v2/ {
        try_files $uri $uri/ /api/v2/index.php?$query_string;
    }
    
    # Mobile PWA Routes
    location /mobile/ {
        try_files $uri $uri/ /mobile/index.php?$query_string;
    }
    
    # PHP Processing
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # Static Files Caching
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Security - Block sensitive files
    location ~ /\.(env|log|sql|md)$ {
        deny all;
    }
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name downrwanda.com;
    return 301 https://$server_name$request_uri;
}
```

### Step 2: Database Setup

#### Create Database
```sql
CREATE DATABASE down_rwanda CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'downrwanda'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON down_rwanda.* TO 'downrwanda'@'localhost';
FLUSH PRIVILEGES;
```

#### Import Schema
```bash
mysql -u downrwanda -p down_rwanda < database/schema.sql
```

### Step 3: Application Configuration

#### Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

#### Required Environment Variables
```env
# Application
APP_ENV=production
APP_DEBUG=false
APP_URL=https://downrwanda.com

# Database
DB_HOST=localhost
DB_DATABASE=down_rwanda
DB_USERNAME=downrwanda
DB_PASSWORD=your_secure_database_password

# Security
JWT_SECRET=your-super-secret-jwt-key-min-32-characters-long
SESSION_SECRET=your-session-secret-key-change-in-production
ENCRYPTION_KEY=your-32-character-encryption-key-here

# External APIs
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# Email
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_FROM_ADDRESS=<EMAIL>
```

#### File Permissions
```bash
# Set proper ownership
chown -R www-data:www-data /var/www/down-rwanda

# Set directory permissions
find /var/www/down-rwanda -type d -exec chmod 755 {} \;

# Set file permissions
find /var/www/down-rwanda -type f -exec chmod 644 {} \;

# Set writable directories
chmod -R 775 /var/www/down-rwanda/uploads
chmod -R 775 /var/www/down-rwanda/cache
chmod -R 775 /var/www/down-rwanda/logs
chmod -R 775 /var/www/down-rwanda/temp

# Secure sensitive files
chmod 600 /var/www/down-rwanda/.env
```

### Step 4: Dependencies Installation

#### PHP Dependencies (if using Composer)
```bash
cd /var/www/down-rwanda
composer install --no-dev --optimize-autoloader
```

#### Frontend Assets (if using npm)
```bash
npm install --production
npm run build
```

### Step 5: System Testing

#### Run System Tests
```bash
# Access the test suite
https://downrwanda.com/test_system.php
```

#### Manual Testing Checklist
- [ ] Homepage loads correctly
- [ ] User registration works
- [ ] User login works
- [ ] Event reporting works
- [ ] Mobile PWA installs
- [ ] API endpoints respond
- [ ] File uploads work
- [ ] Email notifications send
- [ ] SMS notifications send (if configured)

### Step 6: Production Optimizations

#### PHP Configuration (php.ini)
```ini
# Security
expose_php = Off
display_errors = Off
log_errors = On
error_log = /var/log/php/error.log

# Performance
opcache.enable = 1
opcache.memory_consumption = 128
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 60

# File Uploads
upload_max_filesize = 10M
post_max_size = 10M
max_file_uploads = 5

# Session Security
session.cookie_secure = 1
session.cookie_httponly = 1
session.use_strict_mode = 1
```

#### Database Optimization
```sql
-- Add indexes for performance
CREATE INDEX idx_events_location ON events(latitude, longitude);
CREATE INDEX idx_events_created ON events(created_at);
CREATE INDEX idx_events_type ON events(type);
CREATE INDEX idx_notifications_user ON notifications(user_id, is_read);

-- Optimize tables
OPTIMIZE TABLE events;
OPTIMIZE TABLE users;
OPTIMIZE TABLE notifications;
```

### Step 7: Monitoring & Maintenance

#### Log Rotation
```bash
# Add to /etc/logrotate.d/downrwanda
/var/www/down-rwanda/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

#### Backup Script
```bash
#!/bin/bash
# /usr/local/bin/backup-downrwanda.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/downrwanda"
DB_NAME="down_rwanda"
DB_USER="downrwanda"
DB_PASS="your_password"

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/db_$DATE.sql

# Files backup
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /var/www/down-rwanda/uploads

# Clean old backups (keep 30 days)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

#### Cron Jobs
```bash
# Add to crontab
# Cleanup old files every day at 2 AM
0 2 * * * /usr/bin/php /var/www/down-rwanda/cron/cleanup.php

# Sync external events every hour
0 * * * * /usr/bin/php /var/www/down-rwanda/cron/sync_external_events.php

# Backup daily at 3 AM
0 3 * * * /usr/local/bin/backup-downrwanda.sh

# Check location-based notifications every 15 minutes
*/15 * * * * /usr/bin/php /var/www/down-rwanda/notifications/check_location.php
```

### Step 8: Security Hardening

#### Firewall Configuration
```bash
# UFW Configuration
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw enable
```

#### Fail2Ban Configuration
```ini
# /etc/fail2ban/jail.local
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[apache-auth]
enabled = true
port = http,https
filter = apache-auth
logpath = /var/log/apache2/error.log

[apache-badbots]
enabled = true
port = http,https
filter = apache-badbots
logpath = /var/log/apache2/access.log
```

### Step 9: SSL Certificate Setup

#### Let's Encrypt (Certbot)
```bash
# Install Certbot
apt install certbot python3-certbot-apache

# Get certificate
certbot --apache -d downrwanda.com

# Auto-renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
```

### Step 10: Performance Monitoring

#### System Monitoring
- Set up monitoring with tools like Nagios, Zabbix, or New Relic
- Monitor disk space, memory usage, and CPU load
- Set up alerts for system issues

#### Application Monitoring
- Monitor API response times
- Track error rates
- Monitor database performance
- Set up uptime monitoring

## 🔧 Troubleshooting

### Common Issues

#### Database Connection Errors
- Check database credentials in .env
- Verify MySQL service is running
- Check firewall settings

#### File Upload Issues
- Verify directory permissions
- Check PHP upload limits
- Ensure sufficient disk space

#### API Not Working
- Check Apache/Nginx rewrite rules
- Verify PHP extensions are installed
- Check error logs

#### Mobile PWA Issues
- Ensure HTTPS is properly configured
- Check manifest.json syntax
- Verify service worker registration

### Performance Issues
- Enable PHP OPcache
- Optimize database queries
- Implement caching
- Use CDN for static assets

## 📞 Support

For deployment support:
- Email: <EMAIL>
- Documentation: https://docs.downrwanda.com
- GitHub Issues: https://github.com/your-org/down-rwanda/issues

---

**Deployment completed successfully!** 🎉

Your DOWN-RWANDA v2.0 system is now ready for production use.
