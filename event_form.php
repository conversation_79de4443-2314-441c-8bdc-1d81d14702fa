<?php
session_start();
require 'includes/auth_check.php';
?>
<!DOCTYPE html>
<html>
<head>
    <title>Report Accident</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        #map { height: 500px; margin-bottom: 20px; }
        .coordinates { background: #f8f9fa; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>Report New Accident</h2>
        
        <form method="POST" action="add_event.php" enctype="multipart/form-data">
            <div class="mb-3">
                <label class="form-label">Accident Description</label>
                <input type="text" name="title" class="form-control" required>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Location</label>
                <div id="map"></div>
                <div class="coordinates mt-2">
                    Latitude: <span id="lat-display">0</span>, 
                    Longitude: <span id="lng-display">0</span>
                    <input type="hidden" name="latitude" id="latitude" required>
                    <input type="hidden" name="longitude" id="longitude" required>
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Upload Image (Optional)</label>
                <input type="file" name="event_image" class="form-control" accept="image/*">
            </div>
            
            <button type="submit" class="btn btn-primary">Submit Report</button>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Initialize map centered on Rwanda
        const map = L.map('map').setView([-1.9403, 29.8739], 12);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);

        // Add click event to get coordinates
        let marker;
        map.on('click', function(e) {
            const { lat, lng } = e.latlng;
            
            // Update display
            document.getElementById('lat-display').textContent = lat.toFixed(6);
            document.getElementById('lng-display').textContent = lng.toFixed(6);
            document.getElementById('latitude').value = lat;
            document.getElementById('longitude').value = lng;
            
            // Update marker
            if (marker) {
                map.removeLayer(marker);
            }
            marker = L.marker([lat, lng]).addTo(map)
                .bindPopup("Accident Location").openPopup();
        });

        // Get user's current location if available
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (pos) => {
                    const { latitude, longitude } = pos.coords;
                    map.setView([latitude, longitude], 15);
                },
                (err) => console.warn("Geolocation error:", err),
                { timeout: 5000 }
            );
        }
    </script>
</body>
</html>