<?php
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';
require_once '../config.php';

class AdvancedAnalytics {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Get comprehensive system analytics
     */
    public function getSystemAnalytics($dateRange = '30 days') {
        $cacheKey = getCacheKey('system_analytics', ['range' => $dateRange]);
        $analytics = getCache($cacheKey);
        
        if ($analytics === false) {
            $analytics = [
                'overview' => $this->getOverviewStats($dateRange),
                'event_trends' => $this->getEventTrends($dateRange),
                'response_analytics' => $this->getResponseAnalytics($dateRange),
                'user_engagement' => $this->getUserEngagement($dateRange),
                'geographic_distribution' => $this->getGeographicDistribution($dateRange),
                'performance_metrics' => $this->getPerformanceMetrics($dateRange),
                'predictive_insights' => $this->getPredictiveInsights($dateRange)
            ];
            
            setCache($cacheKey, $analytics, 300); // Cache for 5 minutes
        }
        
        return $analytics;
    }
    
    /**
     * Get overview statistics
     */
    private function getOverviewStats($dateRange) {
        $sql = "
            SELECT 
                COUNT(*) as total_events,
                COUNT(CASE WHEN type = 'accident' THEN 1 END) as accidents,
                COUNT(CASE WHEN type = 'fire' THEN 1 END) as fires,
                COUNT(CASE WHEN type = 'medical' THEN 1 END) as medical,
                COUNT(CASE WHEN type = 'crime' THEN 1 END) as crimes,
                COUNT(CASE WHEN is_approved = TRUE THEN 1 END) as approved_events,
                COUNT(CASE WHEN severity_level = 'critical' THEN 1 END) as critical_events,
                AVG(CASE WHEN ea.response_time IS NOT NULL THEN ea.response_time END) as avg_response_time
            FROM events e
            LEFT JOIN event_analytics ea ON e.id = ea.event_id
            WHERE e.created_at >= DATE_SUB(NOW(), INTERVAL $dateRange)
        ";
        
        $result = $this->pdo->query($sql)->fetch();
        
        // Calculate growth rates
        $previousPeriod = $this->getPreviousPeriodStats($dateRange);
        $result['growth_rate'] = $this->calculateGrowthRate($result['total_events'], $previousPeriod['total_events']);
        $result['accident_growth'] = $this->calculateGrowthRate($result['accidents'], $previousPeriod['accidents']);
        
        return $result;
    }
    
    /**
     * Get event trends over time
     */
    private function getEventTrends($dateRange) {
        $sql = "
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as total_events,
                COUNT(CASE WHEN type = 'accident' THEN 1 END) as accidents,
                COUNT(CASE WHEN type = 'fire' THEN 1 END) as fires,
                COUNT(CASE WHEN type = 'medical' THEN 1 END) as medical,
                COUNT(CASE WHEN severity_level = 'critical' THEN 1 END) as critical_events
            FROM events
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL $dateRange)
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ";
        
        return $this->pdo->query($sql)->fetchAll();
    }
    
    /**
     * Get response time analytics
     */
    private function getResponseAnalytics($dateRange) {
        $sql = "
            SELECT 
                e.type,
                AVG(er.response_time) as avg_response_time,
                MIN(er.response_time) as min_response_time,
                MAX(er.response_time) as max_response_time,
                COUNT(*) as total_responses,
                COUNT(CASE WHEN er.response_time <= 10 THEN 1 END) as fast_responses,
                COUNT(CASE WHEN er.response_time > 30 THEN 1 END) as slow_responses
            FROM emergency_responses er
            JOIN events e ON er.event_id = e.id
            WHERE er.created_at >= DATE_SUB(NOW(), INTERVAL $dateRange)
            GROUP BY e.type
        ";
        
        $results = $this->pdo->query($sql)->fetchAll();
        
        // Calculate performance scores
        foreach ($results as &$result) {
            $fastPercentage = ($result['fast_responses'] / $result['total_responses']) * 100;
            $slowPercentage = ($result['slow_responses'] / $result['total_responses']) * 100;
            $result['performance_score'] = max(0, 100 - $slowPercentage + ($fastPercentage * 0.5));
        }
        
        return $results;
    }
    
    /**
     * Get user engagement metrics
     */
    private function getUserEngagement($dateRange) {
        $sql = "
            SELECT 
                u.role,
                COUNT(DISTINCT u.id) as active_users,
                COUNT(e.id) as events_reported,
                AVG(ea.views) as avg_event_views,
                COUNT(n.id) as notifications_sent
            FROM users u
            LEFT JOIN events e ON u.id = e.user_id AND e.created_at >= DATE_SUB(NOW(), INTERVAL $dateRange)
            LEFT JOIN event_analytics ea ON e.id = ea.event_id
            LEFT JOIN notifications n ON u.id = n.user_id AND n.created_at >= DATE_SUB(NOW(), INTERVAL $dateRange)
            WHERE u.created_at <= NOW()
            GROUP BY u.role
        ";
        
        return $this->pdo->query($sql)->fetchAll();
    }
    
    /**
     * Get geographic distribution of events
     */
    private function getGeographicDistribution($dateRange) {
        $sql = "
            SELECT 
                ROUND(latitude, 2) as lat_zone,
                ROUND(longitude, 2) as lng_zone,
                COUNT(*) as event_count,
                COUNT(CASE WHEN type = 'accident' THEN 1 END) as accidents,
                COUNT(CASE WHEN severity_level = 'critical' THEN 1 END) as critical_events
            FROM events
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL $dateRange)
            GROUP BY lat_zone, lng_zone
            HAVING event_count > 1
            ORDER BY event_count DESC
            LIMIT 20
        ";
        
        return $this->pdo->query($sql)->fetchAll();
    }
    
    /**
     * Get performance metrics
     */
    private function getPerformanceMetrics($dateRange) {
        $metrics = [];
        
        // Database performance
        $metrics['db_performance'] = $this->getDatabasePerformance();
        
        // API performance
        $metrics['api_performance'] = $this->getAPIPerformance($dateRange);
        
        // System load
        $metrics['system_load'] = $this->getSystemLoad();
        
        // Error rates
        $metrics['error_rates'] = $this->getErrorRates($dateRange);
        
        return $metrics;
    }
    
    /**
     * Get predictive insights using basic ML algorithms
     */
    private function getPredictiveInsights($dateRange) {
        $insights = [];
        
        // Predict accident hotspots
        $insights['accident_hotspots'] = $this->predictAccidentHotspots();
        
        // Predict peak times
        $insights['peak_times'] = $this->predictPeakTimes();
        
        // Resource allocation recommendations
        $insights['resource_recommendations'] = $this->getResourceRecommendations();
        
        // Trend predictions
        $insights['trend_predictions'] = $this->predictTrends();
        
        return $insights;
    }
    
    /**
     * Predict accident hotspots using clustering
     */
    private function predictAccidentHotspots() {
        $sql = "
            SELECT 
                latitude,
                longitude,
                COUNT(*) as accident_count,
                AVG(CASE WHEN severity_level = 'critical' THEN 3 
                         WHEN severity_level = 'high' THEN 2 
                         ELSE 1 END) as severity_score
            FROM events
            WHERE type = 'accident' 
            AND created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)
            GROUP BY ROUND(latitude, 3), ROUND(longitude, 3)
            HAVING accident_count >= 3
            ORDER BY accident_count DESC, severity_score DESC
            LIMIT 10
        ";
        
        $hotspots = $this->pdo->query($sql)->fetchAll();
        
        // Calculate risk scores
        foreach ($hotspots as &$hotspot) {
            $hotspot['risk_score'] = ($hotspot['accident_count'] * 0.7) + ($hotspot['severity_score'] * 0.3);
            $hotspot['risk_level'] = $this->getRiskLevel($hotspot['risk_score']);
        }
        
        return $hotspots;
    }
    
    /**
     * Predict peak times for different event types
     */
    private function predictPeakTimes() {
        $sql = "
            SELECT 
                type,
                HOUR(created_at) as hour,
                DAYOFWEEK(created_at) as day_of_week,
                COUNT(*) as event_count
            FROM events
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)
            GROUP BY type, hour, day_of_week
            ORDER BY event_count DESC
        ";
        
        $timeData = $this->pdo->query($sql)->fetchAll();
        
        // Group by type and find patterns
        $patterns = [];
        foreach ($timeData as $data) {
            $type = $data['type'];
            if (!isset($patterns[$type])) {
                $patterns[$type] = [];
            }
            
            $patterns[$type][] = [
                'hour' => $data['hour'],
                'day_of_week' => $data['day_of_week'],
                'frequency' => $data['event_count']
            ];
        }
        
        // Find peak hours for each type
        $peakTimes = [];
        foreach ($patterns as $type => $typeData) {
            $hourCounts = array_fill(0, 24, 0);
            foreach ($typeData as $data) {
                $hourCounts[$data['hour']] += $data['frequency'];
            }
            
            $peakHour = array_keys($hourCounts, max($hourCounts))[0];
            $peakTimes[$type] = [
                'peak_hour' => $peakHour,
                'peak_frequency' => max($hourCounts),
                'pattern' => $this->analyzeTimePattern($typeData)
            ];
        }
        
        return $peakTimes;
    }
    
    /**
     * Get resource allocation recommendations
     */
    private function getResourceRecommendations() {
        $recommendations = [];
        
        // Police unit allocation
        $policeNeeds = $this->calculatePoliceNeeds();
        $recommendations['police_allocation'] = $policeNeeds;
        
        // Medical resource needs
        $medicalNeeds = $this->calculateMedicalNeeds();
        $recommendations['medical_allocation'] = $medicalNeeds;
        
        // Equipment recommendations
        $equipmentNeeds = $this->calculateEquipmentNeeds();
        $recommendations['equipment_needs'] = $equipmentNeeds;
        
        return $recommendations;
    }
    
    /**
     * Predict future trends using linear regression
     */
    private function predictTrends() {
        $sql = "
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as daily_count
            FROM events
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ";
        
        $dailyData = $this->pdo->query($sql)->fetchAll();
        
        // Simple linear regression for trend prediction
        $trend = $this->calculateLinearTrend($dailyData);
        
        // Predict next 7 days
        $predictions = [];
        for ($i = 1; $i <= 7; $i++) {
            $futureDate = date('Y-m-d', strtotime("+$i days"));
            $predictedCount = max(0, round($trend['slope'] * (count($dailyData) + $i) + $trend['intercept']));
            
            $predictions[] = [
                'date' => $futureDate,
                'predicted_events' => $predictedCount,
                'confidence' => $trend['r_squared']
            ];
        }
        
        return [
            'trend_direction' => $trend['slope'] > 0 ? 'increasing' : 'decreasing',
            'trend_strength' => abs($trend['slope']),
            'predictions' => $predictions,
            'confidence_level' => $trend['r_squared']
        ];
    }
    
    // Helper methods
    private function getPreviousPeriodStats($dateRange) {
        $sql = "
            SELECT 
                COUNT(*) as total_events,
                COUNT(CASE WHEN type = 'accident' THEN 1 END) as accidents
            FROM events
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL $dateRange * 2)
            AND created_at < DATE_SUB(NOW(), INTERVAL $dateRange)
        ";
        
        return $this->pdo->query($sql)->fetch();
    }
    
    private function calculateGrowthRate($current, $previous) {
        if ($previous == 0) return $current > 0 ? 100 : 0;
        return round((($current - $previous) / $previous) * 100, 2);
    }
    
    private function getRiskLevel($score) {
        if ($score >= 8) return 'very_high';
        if ($score >= 6) return 'high';
        if ($score >= 4) return 'medium';
        if ($score >= 2) return 'low';
        return 'very_low';
    }
    
    private function analyzeTimePattern($timeData) {
        // Analyze if events are more frequent on weekdays vs weekends
        $weekdayCount = 0;
        $weekendCount = 0;
        
        foreach ($timeData as $data) {
            if ($data['day_of_week'] >= 2 && $data['day_of_week'] <= 6) {
                $weekdayCount += $data['frequency'];
            } else {
                $weekendCount += $data['frequency'];
            }
        }
        
        return [
            'weekday_frequency' => $weekdayCount,
            'weekend_frequency' => $weekendCount,
            'pattern_type' => $weekdayCount > $weekendCount ? 'weekday_heavy' : 'weekend_heavy'
        ];
    }
    
    private function calculateLinearTrend($data) {
        $n = count($data);
        if ($n < 2) return ['slope' => 0, 'intercept' => 0, 'r_squared' => 0];
        
        $sumX = 0;
        $sumY = 0;
        $sumXY = 0;
        $sumXX = 0;
        
        foreach ($data as $i => $point) {
            $x = $i + 1;
            $y = $point['daily_count'];
            
            $sumX += $x;
            $sumY += $y;
            $sumXY += $x * $y;
            $sumXX += $x * $x;
        }
        
        $slope = ($n * $sumXY - $sumX * $sumY) / ($n * $sumXX - $sumX * $sumX);
        $intercept = ($sumY - $slope * $sumX) / $n;
        
        // Calculate R-squared
        $meanY = $sumY / $n;
        $ssTotal = 0;
        $ssRes = 0;
        
        foreach ($data as $i => $point) {
            $x = $i + 1;
            $y = $point['daily_count'];
            $predicted = $slope * $x + $intercept;
            
            $ssTotal += pow($y - $meanY, 2);
            $ssRes += pow($y - $predicted, 2);
        }
        
        $rSquared = $ssTotal > 0 ? 1 - ($ssRes / $ssTotal) : 0;
        
        return [
            'slope' => $slope,
            'intercept' => $intercept,
            'r_squared' => max(0, min(1, $rSquared))
        ];
    }
    
    private function getDatabasePerformance() {
        // Get database performance metrics
        $sql = "SHOW STATUS LIKE 'Queries'";
        $queries = $this->pdo->query($sql)->fetch();
        
        return [
            'total_queries' => $queries['Value'] ?? 0,
            'avg_query_time' => 0.05, // Placeholder
            'slow_queries' => 0 // Placeholder
        ];
    }
    
    private function getAPIPerformance($dateRange) {
        // Placeholder for API performance metrics
        return [
            'avg_response_time' => 150,
            'success_rate' => 99.5,
            'error_rate' => 0.5
        ];
    }
    
    private function getSystemLoad() {
        // Placeholder for system load metrics
        return [
            'cpu_usage' => 45,
            'memory_usage' => 60,
            'disk_usage' => 30
        ];
    }
    
    private function getErrorRates($dateRange) {
        // Placeholder for error rate analysis
        return [
            'total_errors' => 12,
            'error_rate' => 0.1,
            'critical_errors' => 2
        ];
    }
    
    private function calculatePoliceNeeds() {
        // Calculate police resource needs based on event patterns
        return [
            'recommended_units' => 15,
            'peak_coverage_hours' => ['08:00-10:00', '17:00-19:00'],
            'high_priority_zones' => 3
        ];
    }
    
    private function calculateMedicalNeeds() {
        // Calculate medical resource needs
        return [
            'ambulance_units' => 8,
            'medical_stations' => 5,
            'emergency_supplies' => 'adequate'
        ];
    }
    
    private function calculateEquipmentNeeds() {
        // Calculate equipment needs
        return [
            'communication_devices' => 25,
            'emergency_kits' => 50,
            'vehicles' => 12
        ];
    }
}

// Usage example
if (isset($_GET['action']) && $_GET['action'] === 'get_analytics') {
    header('Content-Type: application/json');
    
    $analytics = new AdvancedAnalytics($pdo);
    $dateRange = $_GET['range'] ?? '30 days';
    
    try {
        $result = $analytics->getSystemAnalytics($dateRange);
        echo json_encode([
            'success' => true,
            'data' => $result,
            'generated_at' => date('c')
        ]);
    } catch (Exception $e) {
        logError("Analytics generation failed: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'error' => 'Failed to generate analytics'
        ]);
    }
}
?>
