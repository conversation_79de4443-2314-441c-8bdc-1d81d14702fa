<?php
function sendPoliceAlert($latitude, $longitude) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT u.phone 
        FROM users u
        WHERE u.role = 'police'
        AND ST_Distance_Sphere(
            POINT(?, ?),
            u.last_location
        ) <= 5000
    ");
    $stmt->execute([$longitude, $latitude]);
    
    while ($officer = $stmt->fetch()) {
        // In production, use an SMS gateway like Twilio
        file_put_contents('logs/police_alerts.log', 
            "[" . date('Y-m-d H:i:s') . "] <PERSON><PERSON> sent to {$officer['phone']}\n", 
            FILE_APPEND
        );
    }
}
?>