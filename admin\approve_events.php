<?php
session_start();
require '../includes/db_connect.php';
require '../includes/auth_check.php';

// Only admins can access
if ($_SESSION['role'] !== 'admin') {
    header("HTTP/1.1 403 Forbidden");
    exit;
}

// Handle approvals
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $stmt = $pdo->prepare("UPDATE events SET is_approved = ? WHERE id = ?");
    $stmt->execute([$_POST['action'] === 'approve' ? 1 : 0, $_POST['event_id']]);
    header("Location: approve_events.php?updated=".$_POST['event_id']);
    exit;
}

// Get pending events
$events = $pdo->query("
    SELECT e.*, u.username 
    FROM events e
    JOIN users u ON e.user_id = u.id
    WHERE e.is_approved = FALSE
")->fetchAll();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Approve Events</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Pending Event Approvals</h2>
        
        <?php if(empty($events)): ?>
            <div class="alert alert-info">No events pending approval</div>
        <?php else: ?>
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Event</th>
                        <th>Organizer</th>
                        <th>Type</th>
                        <th>Payment Proof</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach($events as $event): ?>
                    <tr>
                        <td><?= htmlspecialchars($event['title']) ?></td>
                        <td><?= htmlspecialchars($event['username']) ?></td>
                        <td><?= ucfirst($event['type']) ?></td>
                        <td>
                            <?php if($event['payment_proof']): ?>
                                <a href="<?= $event['payment_proof'] ?>" target="_blank">View Receipt</a>
                            <?php else: ?>
                                <span class="text-muted">Free event</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="event_id" value="<?= $event['id'] ?>">
                                <button type="submit" name="action" value="approve" class="btn btn-sm btn-success">Approve</button>
                            </form>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="event_id" value="<?= $event['id'] ?>">
                                <button type="submit" name="action" value="reject" class="btn btn-sm btn-danger">Reject</button>
                            </form>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</body>
</html>