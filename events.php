<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require 'includes/db_connect.php';
require 'includes/auth_check.php';
require 'includes/functions.php';
require 'config.php';

// Get filter parameters
$type = isset($_GET['type']) ? sanitizeInput($_GET['type']) : '';
$severity = isset($_GET['severity']) ? sanitizeInput($_GET['severity']) : '';
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Build query
$whereConditions = ["e.is_approved = TRUE"];
$params = [];

if ($type) {
    $whereConditions[] = "e.type = ?";
    $params[] = $type;
}

if ($severity) {
    $whereConditions[] = "e.severity_level = ?";
    $params[] = $severity;
}

if ($search) {
    $whereConditions[] = "(e.title LIKE ? OR e.description LIKE ? OR e.location_description LIKE ?)";
    $searchTerm = "%$search%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

$whereClause = implode(' AND ', $whereConditions);

// Get events
$query = "
    SELECT e.*, u.username, ea.views,
           (SELECT COUNT(*) FROM event_images ei WHERE ei.event_id = e.id) as image_count
    FROM events e
    LEFT JOIN users u ON e.user_id = u.id
    LEFT JOIN event_analytics ea ON e.id = ea.event_id
    WHERE $whereClause
    ORDER BY e.created_at DESC
    LIMIT $limit OFFSET $offset
";

$stmt = $pdo->prepare($query);
$stmt->execute($params);
$events = $stmt->fetchAll();

// Get total count for pagination
$countQuery = "SELECT COUNT(*) FROM events e WHERE $whereClause";
$countStmt = $pdo->prepare($countQuery);
$countStmt->execute($params);
$totalEvents = $countStmt->fetchColumn();
$totalPages = ceil($totalEvents / $limit);

// Get statistics
$stats = $pdo->query("
    SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN type = 'accident' THEN 1 END) as accidents,
        COUNT(CASE WHEN type = 'fire' THEN 1 END) as fires,
        COUNT(CASE WHEN type = 'medical' THEN 1 END) as medical,
        COUNT(CASE WHEN severity_level = 'critical' THEN 1 END) as critical
    FROM events WHERE is_approved = TRUE
")->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Events | <?= APP_NAME ?></title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 5px 20px rgba(0, 0, 0, 0.15);
            --border-radius: 15px;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            box-shadow: var(--shadow-light);
        }

        .navbar-brand {
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .main-content {
            padding: 2rem 0;
        }

        .page-header {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-light);
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .stats-row {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
            flex-wrap: wrap;
        }

        .stat-item {
            background: rgba(52, 152, 219, 0.1);
            border-radius: 8px;
            padding: 0.75rem 1rem;
            text-align: center;
            flex: 1;
            min-width: 120px;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 0.8rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .filters-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-light);
        }

        .filter-chips {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }

        .filter-chip {
            background: #e9ecef;
            border: none;
            border-radius: 20px;
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #6c757d;
        }

        .filter-chip:hover {
            background: var(--secondary-color);
            color: white;
            text-decoration: none;
        }

        .filter-chip.active {
            background: var(--primary-color);
            color: white;
        }

        .search-form {
            display: flex;
            gap: 1rem;
            align-items: end;
        }

        .search-input {
            flex: 1;
        }

        .events-grid {
            display: grid;
            gap: 1.5rem;
        }

        .event-card {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .event-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .event-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
        }

        .event-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .event-type {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .type-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.1rem;
        }

        .type-icon.accident { background: var(--danger-color); }
        .type-icon.fire { background: #fd7e14; }
        .type-icon.medical { background: #e83e8c; }
        .type-icon.crime { background: #6f42c1; }
        .type-icon.show { background: var(--success-color); }
        .type-icon.party { background: #17a2b8; }

        .severity-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .severity-low {
            background: rgba(39, 174, 96, 0.1);
            color: var(--success-color);
        }

        .severity-medium {
            background: rgba(52, 152, 219, 0.1);
            color: var(--secondary-color);
        }

        .severity-high {
            background: rgba(243, 156, 18, 0.1);
            color: var(--warning-color);
        }

        .severity-critical {
            background: rgba(231, 76, 60, 0.1);
            color: var(--danger-color);
        }

        .event-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .event-description {
            color: #6c757d;
            line-height: 1.5;
        }

        .event-footer {
            padding: 1rem 1.5rem;
            background: #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .event-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 0.85rem;
            color: #6c757d;
        }

        .event-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            background: none;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: var(--secondary-color);
            border-color: var(--secondary-color);
            color: white;
        }

        .pagination-wrapper {
            display: flex;
            justify-content: center;
            margin-top: 3rem;
        }

        .pagination .page-link {
            border-radius: 8px;
            margin: 0 0.25rem;
            border: 1px solid #dee2e6;
            color: var(--primary-color);
        }

        .pagination .page-link:hover {
            background: var(--secondary-color);
            border-color: var(--secondary-color);
            color: white;
        }

        .pagination .page-item.active .page-link {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
        }

        .empty-state i {
            font-size: 4rem;
            color: #dee2e6;
            margin-bottom: 1rem;
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 1rem 0;
            }

            .page-header {
                padding: 1.5rem;
            }

            .stats-row {
                gap: 0.5rem;
            }

            .stat-item {
                min-width: 100px;
                padding: 0.5rem;
            }

            .search-form {
                flex-direction: column;
                gap: 1rem;
            }

            .event-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            .event-footer {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-shield-alt me-2"></i>
                DOWN-RWANDA
            </a>

            <div class="d-flex">
                <a href="index.php" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-arrow-left me-1"></i>
                    Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-calendar-alt me-3"></i>
                Events & Incidents
            </h1>
            <p class="text-muted mb-0">Browse and search through reported events and incidents</p>

            <div class="stats-row">
                <div class="stat-item">
                    <div class="stat-number"><?= number_format($stats['total']) ?></div>
                    <div class="stat-label">Total Events</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= number_format($stats['accidents']) ?></div>
                    <div class="stat-label">Accidents</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= number_format($stats['fires']) ?></div>
                    <div class="stat-label">Fire Incidents</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= number_format($stats['medical']) ?></div>
                    <div class="stat-label">Medical</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= number_format($stats['critical']) ?></div>
                    <div class="stat-label">Critical</div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters-card">
            <h5 class="mb-3">
                <i class="fas fa-filter me-2"></i>
                Filters
            </h5>

            <!-- Type Filters -->
            <div class="filter-chips">
                <a href="events.php" class="filter-chip <?= empty($type) ? 'active' : '' ?>">All Types</a>
                <a href="?type=accident<?= $severity ? "&severity=$severity" : '' ?><?= $search ? "&search=$search" : '' ?>"
                   class="filter-chip <?= $type === 'accident' ? 'active' : '' ?>">Accidents</a>
                <a href="?type=fire<?= $severity ? "&severity=$severity" : '' ?><?= $search ? "&search=$search" : '' ?>"
                   class="filter-chip <?= $type === 'fire' ? 'active' : '' ?>">Fire</a>
                <a href="?type=medical<?= $severity ? "&severity=$severity" : '' ?><?= $search ? "&search=$search" : '' ?>"
                   class="filter-chip <?= $type === 'medical' ? 'active' : '' ?>">Medical</a>
                <a href="?type=crime<?= $severity ? "&severity=$severity" : '' ?><?= $search ? "&search=$search" : '' ?>"
                   class="filter-chip <?= $type === 'crime' ? 'active' : '' ?>">Crime</a>
            </div>

            <!-- Severity Filters -->
            <div class="filter-chips">
                <a href="events.php<?= $type ? "?type=$type" : '' ?><?= $search ? ($type ? "&" : "?") . "search=$search" : '' ?>"
                   class="filter-chip <?= empty($severity) ? 'active' : '' ?>">All Severities</a>
                <a href="?severity=low<?= $type ? "&type=$type" : '' ?><?= $search ? "&search=$search" : '' ?>"
                   class="filter-chip <?= $severity === 'low' ? 'active' : '' ?>">Low</a>
                <a href="?severity=medium<?= $type ? "&type=$type" : '' ?><?= $search ? "&search=$search" : '' ?>"
                   class="filter-chip <?= $severity === 'medium' ? 'active' : '' ?>">Medium</a>
                <a href="?severity=high<?= $type ? "&type=$type" : '' ?><?= $search ? "&search=$search" : '' ?>"
                   class="filter-chip <?= $severity === 'high' ? 'active' : '' ?>">High</a>
                <a href="?severity=critical<?= $type ? "&type=$type" : '' ?><?= $search ? "&search=$search" : '' ?>"
                   class="filter-chip <?= $severity === 'critical' ? 'active' : '' ?>">Critical</a>
            </div>

            <!-- Search -->
            <form method="GET" class="search-form">
                <?php if ($type): ?>
                    <input type="hidden" name="type" value="<?= htmlspecialchars($type) ?>">
                <?php endif; ?>
                <?php if ($severity): ?>
                    <input type="hidden" name="severity" value="<?= htmlspecialchars($severity) ?>">
                <?php endif; ?>

                <div class="search-input">
                    <label class="form-label">Search Events</label>
                    <input type="text" class="form-control" name="search"
                           value="<?= htmlspecialchars($search) ?>"
                           placeholder="Search by title, description, or location...">
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                </button>
                <?php if ($search || $type || $severity): ?>
                <a href="events.php" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i>
                </a>
                <?php endif; ?>
            </form>
        </div>

        <!-- Events List -->
        <?php if (empty($events)): ?>
            <div class="empty-state">
                <i class="fas fa-calendar-times"></i>
                <h4>No Events Found</h4>
                <p class="text-muted">No events match your current filters. Try adjusting your search criteria.</p>
                <a href="events.php" class="btn btn-primary">Clear Filters</a>
            </div>
        <?php else: ?>
            <div class="events-grid">
                <?php foreach ($events as $event): ?>
                <div class="event-card" onclick="viewEvent(<?= $event['id'] ?>)">
                    <div class="event-header">
                        <div class="event-meta">
                            <div class="event-type">
                                <div class="type-icon <?= $event['type'] ?>">
                                    <i class="fas fa-<?= getEventIcon($event['type']) ?>"></i>
                                </div>
                                <div>
                                    <div class="fw-bold"><?= ucfirst($event['type']) ?></div>
                                    <small class="text-muted"><?= formatTimeAgo($event['created_at']) ?></small>
                                </div>
                            </div>
                            <div class="severity-badge severity-<?= $event['severity_level'] ?>">
                                <?= ucfirst($event['severity_level']) ?>
                            </div>
                        </div>

                        <h3 class="event-title"><?= htmlspecialchars($event['title']) ?></h3>

                        <?php if ($event['description']): ?>
                        <p class="event-description">
                            <?= htmlspecialchars(substr($event['description'], 0, 150)) ?>
                            <?= strlen($event['description']) > 150 ? '...' : '' ?>
                        </p>
                        <?php endif; ?>
                    </div>

                    <div class="event-footer">
                        <div class="event-info">
                            <span>
                                <i class="fas fa-user me-1"></i>
                                <?= htmlspecialchars($event['username']) ?>
                            </span>
                            <?php if ($event['location_description']): ?>
                            <span>
                                <i class="fas fa-map-marker-alt me-1"></i>
                                <?= htmlspecialchars($event['location_description']) ?>
                            </span>
                            <?php endif; ?>
                            <?php if ($event['views']): ?>
                            <span>
                                <i class="fas fa-eye me-1"></i>
                                <?= number_format($event['views']) ?> views
                            </span>
                            <?php endif; ?>
                            <?php if ($event['image_count'] > 0): ?>
                            <span>
                                <i class="fas fa-images me-1"></i>
                                <?= $event['image_count'] ?> images
                            </span>
                            <?php endif; ?>
                        </div>

                        <div class="event-actions">
                            <button class="action-btn" onclick="event.stopPropagation(); viewOnMap(<?= $event['latitude'] ?>, <?= $event['longitude'] ?>)">
                                <i class="fas fa-map-marked-alt me-1"></i>
                                Map
                            </button>
                            <button class="action-btn" onclick="event.stopPropagation(); shareEvent(<?= $event['id'] ?>)">
                                <i class="fas fa-share me-1"></i>
                                Share
                            </button>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
            <div class="pagination-wrapper">
                <nav>
                    <ul class="pagination">
                        <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $page - 1 ?><?= $type ? "&type=$type" : '' ?><?= $severity ? "&severity=$severity" : '' ?><?= $search ? "&search=$search" : '' ?>">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                            <a class="page-link" href="?page=<?= $i ?><?= $type ? "&type=$type" : '' ?><?= $severity ? "&severity=$severity" : '' ?><?= $search ? "&search=$search" : '' ?>">
                                <?= $i ?>
                            </a>
                        </li>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $page + 1 ?><?= $type ? "&type=$type" : '' ?><?= $severity ? "&severity=$severity" : '' ?><?= $search ? "&search=$search" : '' ?>">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewEvent(eventId) {
            window.location.href = `event.php?id=${eventId}`;
        }

        function viewOnMap(lat, lng) {
            window.open(`map.php?lat=${lat}&lng=${lng}&zoom=16`, '_blank');
        }

        function shareEvent(eventId) {
            const url = `${window.location.origin}/event.php?id=${eventId}`;

            if (navigator.share) {
                navigator.share({
                    title: 'Event Report',
                    url: url
                });
            } else {
                navigator.clipboard.writeText(url).then(() => {
                    alert('Event link copied to clipboard!');
                });
            }
        }

        // Auto-refresh every 2 minutes
        setTimeout(() => {
            location.reload();
        }, 120000);
    </script>
</body>
</html>

?>
