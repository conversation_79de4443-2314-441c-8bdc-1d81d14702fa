<?php

function handleUsers($method, $id, $user) {
    switch ($method) {
        case 'GET':
            if ($id) {
                if ($id === 'profile') {
                    getUserProfile($user);
                } else {
                    getUser($id, $user);
                }
            } else {
                getUsers($user);
            }
            break;
            
        case 'PUT':
            if ($id === 'profile') {
                updateUserProfile($user);
            } elseif ($id === 'location') {
                updateUserLocation($user);
            } else {
                updateUser($id, $user);
            }
            break;
            
        case 'DELETE':
            if (!$id) {
                http_response_code(400);
                echo json_encode(['error' => 'User ID required']);
                return;
            }
            deleteUser($id, $user);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
}

function getUsers($user) {
    global $pdo;
    
    // Only admins can view all users
    if ($user['role'] !== 'admin') {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        return;
    }
    
    $page = intval($_GET['page'] ?? 1);
    $limit = min(intval($_GET['limit'] ?? 20), 100);
    $offset = ($page - 1) * $limit;
    
    $role = sanitizeInput($_GET['role'] ?? '');
    $search = sanitizeInput($_GET['search'] ?? '');
    
    try {
        $whereConditions = [];
        $params = [];
        
        if ($role) {
            $whereConditions[] = "role = ?";
            $params[] = $role;
        }
        
        if ($search) {
            $whereConditions[] = "(username LIKE ? OR email LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        $whereClause = !empty($whereConditions) ? "WHERE " . implode(" AND ", $whereConditions) : "";
        
        $query = "
            SELECT id, username, email, phone, role, is_verified, created_at,
                   (SELECT COUNT(*) FROM events WHERE user_id = users.id) as event_count
            FROM users 
            $whereClause
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        ";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        $users = $stmt->fetchAll();
        
        // Get total count
        $countQuery = "SELECT COUNT(*) as total FROM users $whereClause";
        $countStmt = $pdo->prepare($countQuery);
        $countStmt->execute(array_slice($params, 0, -2));
        $totalCount = $countStmt->fetch()['total'];
        
        echo json_encode([
            'success' => true,
            'data' => $users,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => intval($totalCount),
                'pages' => ceil($totalCount / $limit)
            ]
        ]);
        
    } catch (PDOException $e) {
        logError("Failed to get users: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to retrieve users']);
    }
}

function getUser($id, $user) {
    global $pdo;
    
    // Users can only view their own profile unless they're admin
    if ($id != $user['user_id'] && $user['role'] !== 'admin') {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            SELECT id, username, email, phone, role, is_verified, avatar, created_at,
                   (SELECT COUNT(*) FROM events WHERE user_id = ?) as event_count,
                   (SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = FALSE) as unread_notifications
            FROM users WHERE id = ?
        ");
        $stmt->execute([$id, $id, $id]);
        $userData = $stmt->fetch();
        
        if (!$userData) {
            http_response_code(404);
            echo json_encode(['error' => 'User not found']);
            return;
        }
        
        // Get recent events for this user
        $eventsStmt = $pdo->prepare("
            SELECT id, title, type, created_at, is_approved 
            FROM events 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT 5
        ");
        $eventsStmt->execute([$id]);
        $userData['recent_events'] = $eventsStmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'data' => $userData
        ]);
        
    } catch (PDOException $e) {
        logError("Failed to get user: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to retrieve user']);
    }
}

function getUserProfile($user) {
    getUser($user['user_id'], $user);
}

function updateUserProfile($user) {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    try {
        $updateFields = [];
        $params = [];
        
        if (isset($input['email']) && validateEmail($input['email'])) {
            $updateFields[] = "email = ?";
            $params[] = sanitizeInput($input['email']);
        }
        
        if (isset($input['phone']) && validatePhone($input['phone'])) {
            $updateFields[] = "phone = ?";
            $params[] = sanitizeInput($input['phone']);
        }
        
        if (isset($input['password']) && strlen($input['password']) >= 6) {
            $updateFields[] = "password = ?";
            $params[] = hashPassword($input['password']);
        }
        
        if (empty($updateFields)) {
            http_response_code(400);
            echo json_encode(['error' => 'No valid fields to update']);
            return;
        }
        
        $params[] = $user['user_id'];
        $sql = "UPDATE users SET " . implode(", ", $updateFields) . " WHERE id = ?";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        echo json_encode([
            'success' => true,
            'message' => 'Profile updated successfully'
        ]);
        
        logActivity("User profile updated via API", 'INFO', [
            'user_id' => $user['user_id']
        ]);
        
    } catch (PDOException $e) {
        if ($e->getCode() == 23000) {
            http_response_code(409);
            echo json_encode(['error' => 'Email already exists']);
        } else {
            logError("Failed to update profile: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Failed to update profile']);
        }
    }
}

function updateUserLocation($user) {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    $latitude = floatval($input['lat'] ?? 0);
    $longitude = floatval($input['lng'] ?? 0);
    
    if (!validateCoordinates($latitude, $longitude)) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid coordinates']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("UPDATE users SET last_location = POINT(?, ?) WHERE id = ?");
        $stmt->execute([$longitude, $latitude, $user['user_id']]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Location updated successfully'
        ]);
        
        logActivity("User location updated", 'DEBUG', [
            'user_id' => $user['user_id'],
            'location' => "$latitude, $longitude"
        ]);
        
    } catch (PDOException $e) {
        logError("Failed to update location: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update location']);
    }
}

function updateUser($id, $user) {
    global $pdo;
    
    // Only admins can update other users
    if ($user['role'] !== 'admin') {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    try {
        $updateFields = [];
        $params = [];
        
        if (isset($input['role'])) {
            $updateFields[] = "role = ?";
            $params[] = sanitizeInput($input['role']);
        }
        
        if (isset($input['is_verified'])) {
            $updateFields[] = "is_verified = ?";
            $params[] = (bool)$input['is_verified'];
        }
        
        if (empty($updateFields)) {
            http_response_code(400);
            echo json_encode(['error' => 'No valid fields to update']);
            return;
        }
        
        $params[] = $id;
        $sql = "UPDATE users SET " . implode(", ", $updateFields) . " WHERE id = ?";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        echo json_encode([
            'success' => true,
            'message' => 'User updated successfully'
        ]);
        
        logActivity("User updated by admin", 'INFO', [
            'target_user_id' => $id,
            'admin_user_id' => $user['user_id']
        ]);
        
    } catch (PDOException $e) {
        logError("Failed to update user: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update user']);
    }
}

function deleteUser($id, $user) {
    global $pdo;
    
    // Only admins can delete users, and they can't delete themselves
    if ($user['role'] !== 'admin' || $id == $user['user_id']) {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        return;
    }
    
    try {
        $pdo->beginTransaction();
        
        // Archive user's events
        $pdo->prepare("
            INSERT INTO archived_events (original_id, title, type, description, latitude, longitude, is_paid, payment_proof, is_approved, user_id, created_at)
            SELECT id, title, type, description, latitude, longitude, is_paid, payment_proof, is_approved, user_id, created_at
            FROM events WHERE user_id = ?
        ")->execute([$id]);
        
        // Delete user's data
        $pdo->prepare("DELETE FROM notifications WHERE user_id = ?")->execute([$id]);
        $pdo->prepare("DELETE FROM user_sessions WHERE user_id = ?")->execute([$id]);
        $pdo->prepare("DELETE FROM api_keys WHERE user_id = ?")->execute([$id]);
        $pdo->prepare("DELETE FROM events WHERE user_id = ?")->execute([$id]);
        $pdo->prepare("DELETE FROM users WHERE id = ?")->execute([$id]);
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'User deleted successfully'
        ]);
        
        logActivity("User deleted by admin", 'WARNING', [
            'deleted_user_id' => $id,
            'admin_user_id' => $user['user_id']
        ]);
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        logError("Failed to delete user: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete user']);
    }
}
?>
