<?php
require 'includes/db_connect.php';
require 'includes/auth_check.php';

// Get all approved events
$events = $pdo->query("SELECT * FROM events WHERE is_approved = TRUE")->fetchAll();

// Get external events
$externalEvents = $pdo->query("SELECT * FROM external_events")->fetchAll();
?>
<!DOCTYPE html>
<html>
<head>
    <title>DOWN-RWANDA</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        #map { height: 600px; }
        .paid-event { border-left: 4px solid #28a745; }
        .accident-event { border-left: 4px solid #dc3545; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">DOWN-RWANDA</a>
            <div class="d-flex">
                <?php if($_SESSION['role'] === 'organization'): ?>
                    <a href="org_dashboard.php" class="btn btn-sm btn-success me-2">Organization Portal</a>
                <?php endif; ?>
                <a href="includes/logout.php" class="btn btn-sm btn-danger">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Submit New Event</h5>
                    </div>
                    <div class="card-body">
                        <form action="api/add_event.php" method="POST" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label class="form-label">Event Type</label>
                                <select name="type" class="form-select" required>
                                    <option value="accident">Accident (Free)</option>
                                    <option value="show">Show/Concert</option>
                                    <option value="party">Party</option>
                                </select>
                            </div>
                            
                            <?php if($_SESSION['role'] === 'organization'): ?>
                                <div id="payment-section" class="mb-3 d-none">
                                    <label class="form-label">Payment Proof (2000 RWF)</label>
                                    <input type="file" name="payment_proof" class="form-control">
                                    <small class="text-muted">Required for non-accident events</small>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Other form fields -->
                            <button type="submit" class="btn btn-primary w-100">Submit Event</button>
                        </form>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5>External Event Sources</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showKigaliToday" checked>
                            <label class="form-check-label" for="showKigaliToday">Kigali Today</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showIgheCom" checked>
                            <label class="form-check-label" for="showIgheCom">IGHE.com</label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-8">
                <div id="map"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>

    const eventsMap = L.map('map').setView([-1.9403, 29.8739], 12);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(eventsMap);

    // Fetch and display events
    fetch('get_events.php?type=accident')
        .then(response => response.json())
        .then(events => {
            events.forEach(event => {
                const popupContent = `
                    <h5>${event.title}</h5>
                    ${event.image_path ? `<img src="${event.image_path}" style="max-width:200px;max-height:200px;">` : ''}
                    <p>Reported by: ${event.username}</p>
                    <small>${new Date(event.created_at).toLocaleString()}</small>
                `;
                
                L.marker([event.latitude, event.longitude])
                    .addTo(eventsMap)
                    .bindPopup(popupContent);
            });
        });


        // Toggle payment section based on event type
        document.querySelector('select[name="type"]').addEventListener('change', function() {
            const paymentSection = document.getElementById('payment-section');
            if(this.value === 'accident') {
                paymentSection?.classList.add('d-none');
            } else {
                paymentSection?.classList.remove('d-none');
            }
        });
    </script>
</body>
</html>