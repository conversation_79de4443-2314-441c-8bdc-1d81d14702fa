<?php
session_start();
require 'includes/db_connect.php';
require 'includes/auth_check.php';
require 'includes/functions.php';
require 'config.php';

// Get dashboard statistics
$stats = $pdo->query("
    SELECT
        COUNT(*) as total_events,
        COUNT(CASE WHEN type = 'accident' THEN 1 END) as accidents,
        COUNT(CASE WHEN type = 'fire' THEN 1 END) as fires,
        COUNT(CASE WHEN type = 'medical' THEN 1 END) as medical,
        COUNT(CASE WHEN severity_level = 'critical' THEN 1 END) as critical_events,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as events_24h
    FROM events WHERE is_approved = TRUE
")->fetch();

// Get recent events
$recentEvents = $pdo->query("
    SELECT e.*, u.username, ea.views
    FROM events e
    LEFT JOIN users u ON e.user_id = u.id
    LEFT JOIN event_analytics ea ON e.id = ea.event_id
    WHERE e.is_approved = TRUE
    ORDER BY e.created_at DESC
    LIMIT 10
")->fetchAll();

// Get user's notifications count
$notificationCount = 0;
if (isset($_SESSION['user_id'])) {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = FALSE");
    $stmt->execute([$_SESSION['user_id']]);
    $notificationCount = $stmt->fetchColumn();
}

// Check for welcome message
$showWelcome = isset($_GET['welcome']) && $_GET['welcome'] == '1';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard | <?= APP_NAME ?></title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/leaflet@1.9.4/dist/leaflet.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-bg: #f8f9fa;
            --dark-bg: #2c3e50;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 5px 20px rgba(0, 0, 0, 0.15);
            --shadow-heavy: 0 10px 30px rgba(0, 0, 0, 0.2);
            --border-radius: 15px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #333;
        }

        /* Navigation */
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            padding: 1rem 0;
            box-shadow: var(--shadow-light);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-link {
            font-weight: 500;
            color: var(--primary-color) !important;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            color: var(--secondary-color) !important;
        }

        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background: var(--secondary-color);
            border-radius: 2px;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Main Content */
        .main-content {
            padding: 2rem 0;
        }

        .page-header {
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: var(--shadow-light);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .stat-card.danger::before { background: var(--danger-color); }
        .stat-card.warning::before { background: var(--warning-color); }
        .stat-card.success::before { background: var(--success-color); }
        .stat-card.info::before { background: var(--info-color); }

        .stat-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stat-icon.danger { background: var(--danger-color); }
        .stat-icon.warning { background: var(--warning-color); }
        .stat-icon.success { background: var(--success-color); }
        .stat-icon.info { background: var(--info-color); }
        .stat-icon.primary { background: var(--secondary-color); }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .stat-change {
            font-size: 0.8rem;
            font-weight: 500;
            margin-top: 0.5rem;
        }

        .stat-change.positive {
            color: var(--success-color);
        }

        .stat-change.negative {
            color: var(--danger-color);
        }

        /* Cards */
        .custom-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
            border: 1px solid rgba(0, 0, 0, 0.05);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .custom-card:hover {
            box-shadow: var(--shadow-medium);
        }

        .card-header-custom {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            padding: 1.25rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        .card-body-custom {
            padding: 1.5rem;
        }

        /* Map */
        #map {
            height: 500px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
        }

        /* Event List */
        .event-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 0.75rem;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .event-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .event-icon {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: white;
            font-size: 1.1rem;
        }

        .event-icon.accident { background: var(--danger-color); }
        .event-icon.fire { background: #fd7e14; }
        .event-icon.medical { background: #e83e8c; }
        .event-icon.crime { background: #6f42c1; }
        .event-icon.show { background: var(--success-color); }
        .event-icon.party { background: var(--info-color); }

        .event-content {
            flex: 1;
        }

        .event-title {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.25rem;
        }

        .event-meta {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .event-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-critical {
            background: rgba(231, 76, 60, 0.1);
            color: var(--danger-color);
        }

        .badge-high {
            background: rgba(243, 156, 18, 0.1);
            color: var(--warning-color);
        }

        .badge-medium {
            background: rgba(52, 152, 219, 0.1);
            color: var(--secondary-color);
        }

        .badge-low {
            background: rgba(39, 174, 96, 0.1);
            color: var(--success-color);
        }

        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .action-btn {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            text-decoration: none;
            color: var(--primary-color);
            transition: all 0.3s ease;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;
        }

        .action-btn:hover {
            border-color: var(--secondary-color);
            color: var(--secondary-color);
            text-decoration: none;
            transform: translateY(-3px);
            box-shadow: var(--shadow-medium);
        }

        .action-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .action-title {
            font-weight: 600;
            font-size: 1rem;
        }

        .action-desc {
            font-size: 0.85rem;
            color: #6c757d;
        }

        /* Welcome Alert */
        .welcome-alert {
            background: linear-gradient(135deg, var(--success-color), #229954);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
            animation: slideDown 0.5s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }

            .quick-actions {
                grid-template-columns: repeat(2, 1fr);
            }

            .page-title {
                font-size: 1.5rem;
            }

            .main-content {
                padding: 1rem 0;
            }
        }

        @media (max-width: 576px) {
            .quick-actions {
                grid-template-columns: 1fr;
            }

            .action-btn {
                padding: 1rem;
            }

            .action-icon {
                width: 50px;
                height: 50px;
                font-size: 1.25rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-shield-alt me-2"></i>
                DOWN-RWANDA
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-home me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">
                            <i class="fas fa-calendar-alt me-1"></i>Events
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="map.php">
                            <i class="fas fa-map-marked-alt me-1"></i>Live Map
                        </a>
                    </li>
                    <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'police'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard/advanced.php">
                            <i class="fas fa-chart-line me-1"></i>Analytics
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link position-relative" href="notifications.php">
                            <i class="fas fa-bell"></i>
                            <?php if ($notificationCount > 0): ?>
                                <span class="notification-badge"><?= $notificationCount ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <?= htmlspecialchars($_SESSION['username']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user me-2"></i>Profile
                            </a></li>
                            <li><a class="dropdown-item" href="settings.php">
                                <i class="fas fa-cog me-2"></i>Settings
                            </a></li>
                            <?php if ($_SESSION['role'] === 'organization'): ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="org_dashboard.php">
                                <i class="fas fa-building me-2"></i>Organization Portal
                            </a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="mobile/">
                                <i class="fas fa-mobile-alt me-2"></i>Mobile App
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container main-content">
        <!-- Welcome Alert -->
        <?php if ($showWelcome): ?>
        <div class="alert welcome-alert alert-dismissible fade show" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-check-circle fa-2x me-3"></i>
                <div>
                    <h5 class="mb-1">Welcome to DOWN-RWANDA!</h5>
                    <p class="mb-0">Your account has been created successfully. Start by exploring the dashboard or reporting your first event.</p>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-tachometer-alt me-3"></i>
                Emergency Response Dashboard
            </h1>
            <p class="page-subtitle">
                Real-time monitoring and management of emergency events across Rwanda
            </p>
        </div>

        <!-- Statistics Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon primary">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                </div>
                <div class="stat-number"><?= number_format($stats['total_events']) ?></div>
                <div class="stat-label">Total Events</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up me-1"></i>
                    +<?= $stats['events_24h'] ?> in last 24h
                </div>
            </div>

            <div class="stat-card danger">
                <div class="stat-header">
                    <div class="stat-icon danger">
                        <i class="fas fa-car-crash"></i>
                    </div>
                </div>
                <div class="stat-number"><?= number_format($stats['accidents']) ?></div>
                <div class="stat-label">Accidents Reported</div>
                <div class="stat-change">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Requires immediate attention
                </div>
            </div>

            <div class="stat-card warning">
                <div class="stat-header">
                    <div class="stat-icon warning">
                        <i class="fas fa-fire"></i>
                    </div>
                </div>
                <div class="stat-number"><?= number_format($stats['fires']) ?></div>
                <div class="stat-label">Fire Incidents</div>
                <div class="stat-change">
                    <i class="fas fa-clock me-1"></i>
                    Emergency response active
                </div>
            </div>

            <div class="stat-card info">
                <div class="stat-header">
                    <div class="stat-icon info">
                        <i class="fas fa-ambulance"></i>
                    </div>
                </div>
                <div class="stat-number"><?= number_format($stats['medical']) ?></div>
                <div class="stat-label">Medical Emergencies</div>
                <div class="stat-change">
                    <i class="fas fa-heartbeat me-1"></i>
                    Medical teams dispatched
                </div>
            </div>

            <div class="stat-card danger">
                <div class="stat-header">
                    <div class="stat-icon danger">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                </div>
                <div class="stat-number"><?= number_format($stats['critical_events']) ?></div>
                <div class="stat-label">Critical Events</div>
                <div class="stat-change negative">
                    <i class="fas fa-arrow-up me-1"></i>
                    High priority response needed
                </div>
            </div>

            <div class="stat-card success">
                <div class="stat-header">
                    <div class="stat-icon success">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="stat-number"><?= $stats['events_24h'] ?></div>
                <div class="stat-label">Events Today</div>
                <div class="stat-change positive">
                    <i class="fas fa-chart-line me-1"></i>
                    Real-time updates
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="report_event.php" class="action-btn">
                <div class="action-icon">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="action-title">Report Event</div>
                <div class="action-desc">Submit a new emergency or event report</div>
            </a>

            <a href="map.php" class="action-btn">
                <div class="action-icon">
                    <i class="fas fa-map-marked-alt"></i>
                </div>
                <div class="action-title">Live Map</div>
                <div class="action-desc">View real-time events on interactive map</div>
            </a>

            <a href="emergency.php" class="action-btn">
                <div class="action-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="action-title">Emergency Alert</div>
                <div class="action-desc">Send immediate emergency notification</div>
            </a>

            <a href="mobile/" class="action-btn">
                <div class="action-icon">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <div class="action-title">Mobile App</div>
                <div class="action-desc">Access mobile PWA for on-the-go reporting</div>
            </a>
        </div>

        <!-- Main Content Grid -->
        <div class="row">
            <!-- Recent Events -->
            <div class="col-lg-6 mb-4">
                <div class="custom-card">
                    <div class="card-header-custom">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-clock me-2"></i>
                                Recent Events
                            </h5>
                            <a href="events.php" class="btn btn-sm btn-outline-primary">View All</a>
                        </div>
                    </div>
                    <div class="card-body-custom">
                        <?php if (empty($recentEvents)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No recent events to display</p>
                                <a href="report_event.php" class="btn btn-primary">Report First Event</a>
                            </div>
                        <?php else: ?>
                            <?php foreach (array_slice($recentEvents, 0, 5) as $event): ?>
                            <div class="event-item" onclick="viewEvent(<?= $event['id'] ?>)">
                                <div class="event-icon <?= $event['type'] ?>">
                                    <i class="fas fa-<?= getEventIcon($event['type']) ?>"></i>
                                </div>
                                <div class="event-content">
                                    <div class="event-title"><?= htmlspecialchars($event['title']) ?></div>
                                    <div class="event-meta">
                                        By <?= htmlspecialchars($event['username']) ?> •
                                        <?= formatTimeAgo($event['created_at']) ?>
                                        <?php if ($event['views']): ?>
                                        • <?= $event['views'] ?> views
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="event-badge badge-<?= $event['severity_level'] ?>">
                                    <?= ucfirst($event['severity_level']) ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Live Map -->
            <div class="col-lg-6 mb-4">
                <div class="custom-card">
                    <div class="card-header-custom">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-map-marked-alt me-2"></i>
                                Live Event Map
                            </h5>
                            <a href="map.php" class="btn btn-sm btn-outline-primary">Full Screen</a>
                        </div>
                    </div>
                    <div class="card-body-custom p-0">
                        <div id="map"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Initialize map
        const eventsMap = L.map('map').setView([-1.9403, 29.8739], 12);

        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors',
            maxZoom: 18
        }).addTo(eventsMap);

        // Custom marker icons
        const eventIcons = {
            accident: L.divIcon({
                html: '<div style="background-color: #e74c3c; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>',
                className: 'custom-marker',
                iconSize: [20, 20],
                iconAnchor: [10, 10]
            }),
            fire: L.divIcon({
                html: '<div style="background-color: #fd7e14; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>',
                className: 'custom-marker',
                iconSize: [20, 20],
                iconAnchor: [10, 10]
            }),
            medical: L.divIcon({
                html: '<div style="background-color: #e83e8c; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>',
                className: 'custom-marker',
                iconSize: [20, 20],
                iconAnchor: [10, 10]
            }),
            default: L.divIcon({
                html: '<div style="background-color: #3498db; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>',
                className: 'custom-marker',
                iconSize: [20, 20],
                iconAnchor: [10, 10]
            })
        };

        // Load and display events on map
        async function loadMapEvents() {
            try {
                const response = await fetch('api/v2/events?limit=50');
                const result = await response.json();

                if (result.success) {
                    result.data.forEach(event => {
                        const icon = eventIcons[event.type] || eventIcons.default;

                        const popupContent = `
                            <div style="min-width: 200px;">
                                <h6 style="margin-bottom: 8px; color: #2c3e50;">${event.title}</h6>
                                <p style="margin-bottom: 8px; font-size: 0.9rem;">${event.description || 'No description available'}</p>
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <span class="badge" style="background-color: ${getEventColor(event.type)}; color: white; font-size: 10px;">${event.type}</span>
                                    <span class="badge" style="background-color: ${getSeverityColor(event.severity_level)}; color: white; font-size: 10px;">${event.severity_level}</span>
                                </div>
                                <small style="color: #6c757d;">
                                    ${event.time_ago} • by ${event.reporter_name}
                                    ${event.views ? `• ${event.views} views` : ''}
                                </small>
                            </div>
                        `;

                        L.marker([event.latitude, event.longitude], { icon })
                            .addTo(eventsMap)
                            .bindPopup(popupContent);
                    });
                }
            } catch (error) {
                console.error('Error loading map events:', error);
            }
        }

        // Helper functions
        function getEventColor(type) {
            const colors = {
                'accident': '#e74c3c',
                'fire': '#fd7e14',
                'medical': '#e83e8c',
                'crime': '#6f42c1',
                'show': '#27ae60',
                'party': '#17a2b8'
            };
            return colors[type] || '#3498db';
        }

        function getSeverityColor(severity) {
            const colors = {
                'critical': '#e74c3c',
                'high': '#f39c12',
                'medium': '#3498db',
                'low': '#27ae60'
            };
            return colors[severity] || '#6c757d';
        }

        function viewEvent(eventId) {
            window.location.href = `event.php?id=${eventId}`;
        }

        function formatTimeAgo(datetime) {
            const now = new Date();
            const time = new Date(datetime);
            const diffInSeconds = Math.floor((now - time) / 1000);

            if (diffInSeconds < 60) return 'just now';
            if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' minutes ago';
            if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' hours ago';
            if (diffInSeconds < 2592000) return Math.floor(diffInSeconds / 86400) + ' days ago';

            return time.toLocaleDateString();
        }

        // Auto-refresh events every 30 seconds
        function autoRefresh() {
            loadMapEvents();
            setTimeout(autoRefresh, 30000);
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadMapEvents();
            autoRefresh();

            // Add click handlers for stat cards
            document.querySelectorAll('.stat-card').forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'translateY(-8px)';
                    setTimeout(() => {
                        this.style.transform = 'translateY(-5px)';
                    }, 150);
                });
            });
        });

        // Real-time notifications
        function checkNotifications() {
            fetch('api/v2/notifications?unread_only=true')
                .then(response => response.json())
                .then(result => {
                    if (result.success && result.data.length > 0) {
                        const badge = document.querySelector('.notification-badge');
                        if (badge) {
                            badge.textContent = result.data.length;
                            badge.style.display = 'flex';
                        }
                    }
                })
                .catch(error => console.error('Error checking notifications:', error));
        }

        // Check notifications every minute
        setInterval(checkNotifications, 60000);
    </script>
</body>
</html>

<?php
// Helper functions for the template
function getEventIcon($type) {
    $icons = [
        'accident' => 'car-crash',
        'fire' => 'fire',
        'medical' => 'ambulance',
        'crime' => 'shield-alt',
        'show' => 'music',
        'party' => 'glass-cheers',
        'sports' => 'futbol'
    ];
    return $icons[$type] ?? 'calendar-alt';
}

function formatTimeAgo($datetime) {
    $now = new DateTime();
    $time = new DateTime($datetime);
    $diff = $now->diff($time);

    if ($diff->days > 30) {
        return $time->format('M j, Y');
    } elseif ($diff->days > 0) {
        return $diff->days . ' day' . ($diff->days > 1 ? 's' : '') . ' ago';
    } elseif ($diff->h > 0) {
        return $diff->h . ' hour' . ($diff->h > 1 ? 's' : '') . ' ago';
    } elseif ($diff->i > 0) {
        return $diff->i . ' minute' . ($diff->i > 1 ? 's' : '') . ' ago';
    } else {
        return 'just now';
    }
}
?>