<?php
session_start();
require '../includes/db_connect.php';
require '../includes/auth_check.php';
require '../includes/functions.php';

if ($_SESSION['role'] !== 'admin' && $_SESSION['role'] !== 'police') {
    header("HTTP/1.1 403 Forbidden");
    exit;
}

// Get system statistics
$stats = getSystemStats();

// Get recent events for timeline
$recentEvents = $pdo->query("
    SELECT e.*, u.username, ea.severity_score
    FROM events e
    LEFT JOIN users u ON e.user_id = u.id
    LEFT JOIN event_analytics ea ON e.id = ea.event_id
    ORDER BY e.created_at DESC
    LIMIT 10
")->fetchAll();

// Get event distribution by type
$eventTypes = $pdo->query("
    SELECT type, COUNT(*) as count
    FROM events
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    GROUP BY type
")->fetchAll();

// Get hourly event distribution for today
$hourlyEvents = $pdo->query("
    SELECT HOUR(created_at) as hour, COUNT(*) as count
    FROM events
    WHERE DATE(created_at) = CURDATE()
    GROUP BY HOUR(created_at)
    ORDER BY hour
")->fetchAll();

// Get response time analytics
$responseAnalytics = $pdo->query("
    SELECT
        AVG(er.response_time) as avg_response_time,
        MIN(er.response_time) as min_response_time,
        MAX(er.response_time) as max_response_time,
        COUNT(*) as total_responses
    FROM emergency_responses er
    WHERE er.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
")->fetch();

// Get active police units
$activeUnits = $pdo->query("
    SELECT pu.*, u.username
    FROM police_units pu
    LEFT JOIN users u ON pu.officer_id = u.id
    WHERE pu.status = 'available'
    ORDER BY pu.last_updated DESC
")->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Dashboard - DOWN-RWANDA</title>

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/leaflet@1.9.4/dist/leaflet.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --dark-color: #34495e;
        }

        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid var(--secondary-color);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stat-card.danger { border-left-color: var(--danger-color); }
        .stat-card.warning { border-left-color: var(--warning-color); }
        .stat-card.success { border-left-color: var(--success-color); }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
        }

        .timeline-item {
            border-left: 3px solid var(--secondary-color);
            padding-left: 1rem;
            margin-bottom: 1rem;
            position: relative;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -6px;
            top: 0;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--secondary-color);
        }

        .timeline-item.critical::before { background: var(--danger-color); }
        .timeline-item.high::before { background: var(--warning-color); }
        .timeline-item.low::before { background: var(--success-color); }

        #realTimeMap {
            height: 400px;
            border-radius: 15px;
        }

        .alert-panel {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .metric-badge {
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            display: inline-block;
            margin: 0.25rem;
        }

        .loading-spinner {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .refresh-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 1000;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        @media (max-width: 768px) {
            .stat-card {
                margin-bottom: 1rem;
            }

            .chart-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-tachometer-alt me-2"></i>
                Advanced Dashboard
            </a>
            <div class="d-flex align-items-center">
                <span class="navbar-text me-3">
                    <i class="fas fa-user-circle me-1"></i>
                    <?= htmlspecialchars($_SESSION['username']) ?>
                </span>
                <button class="btn btn-outline-light btn-sm" onclick="toggleAutoRefresh()">
                    <i class="fas fa-sync-alt" id="refreshIcon"></i>
                    <span id="refreshText">Auto Refresh: OFF</span>
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Alert Panel for Critical Events -->
        <div id="alertPanel" class="alert-panel d-none">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>Critical Alerts</h5>
                    <div id="criticalAlerts"></div>
                </div>
                <button class="btn btn-light btn-sm" onclick="dismissAlerts()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number text-primary"><?= $stats['total_events'] ?></div>
                            <div class="stat-label">Total Events</div>
                        </div>
                        <i class="fas fa-calendar-alt fa-2x text-primary opacity-50"></i>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card danger">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number text-danger"><?= $stats['accidents'] ?></div>
                            <div class="stat-label">Accidents</div>
                        </div>
                        <i class="fas fa-car-crash fa-2x text-danger opacity-50"></i>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card warning">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number text-warning"><?= $stats['events_24h'] ?></div>
                            <div class="stat-label">Last 24 Hours</div>
                        </div>
                        <i class="fas fa-clock fa-2x text-warning opacity-50"></i>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card success">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number text-success"><?= count($activeUnits) ?></div>
                            <div class="stat-label">Active Units</div>
                        </div>
                        <i class="fas fa-shield-alt fa-2x text-success opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-pie me-2"></i>
                        Event Distribution (Last 30 Days)
                    </h5>
                    <canvas id="eventTypeChart" height="300"></canvas>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-line me-2"></i>
                        Hourly Activity (Today)
                    </h5>
                    <canvas id="hourlyChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Map and Timeline Row -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-map-marked-alt me-2"></i>
                        Real-Time Event Map
                    </h5>
                    <div id="realTimeMap"></div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-history me-2"></i>
                        Recent Events
                    </h5>
                    <div id="eventTimeline" style="max-height: 400px; overflow-y: auto;">
                        <?php foreach ($recentEvents as $event): ?>
                        <div class="timeline-item <?= $event['severity_level'] ?? 'medium' ?>">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1"><?= htmlspecialchars($event['title']) ?></h6>
                                    <small class="text-muted">
                                        by <?= htmlspecialchars($event['username']) ?> •
                                        <?= formatTimeAgo($event['created_at']) ?>
                                    </small>
                                </div>
                                <span class="badge bg-<?= $event['type'] === 'accident' ? 'danger' : 'primary' ?>">
                                    <?= ucfirst($event['type']) ?>
                                </span>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Response Analytics -->
        <?php if ($responseAnalytics['total_responses'] > 0): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-stopwatch me-2"></i>
                        Response Time Analytics (Last 7 Days)
                    </h5>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="metric-badge">
                                <strong>Average:</strong> <?= round($responseAnalytics['avg_response_time']) ?> min
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-badge">
                                <strong>Fastest:</strong> <?= $responseAnalytics['min_response_time'] ?> min
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-badge">
                                <strong>Slowest:</strong> <?= $responseAnalytics['max_response_time'] ?> min
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-badge">
                                <strong>Total:</strong> <?= $responseAnalytics['total_responses'] ?> responses
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Loading Spinner -->
    <div class="loading-spinner" id="loadingSpinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2">Updating dashboard...</p>
    </div>

    <!-- Refresh Button -->
    <button class="btn btn-primary refresh-btn" onclick="refreshDashboard()" title="Refresh Dashboard">
        <i class="fas fa-sync-alt"></i>
    </button>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1.6.1/dist/sockjs.min.js"></script>

    <script>
        // Global variables
        let autoRefreshInterval = null;
        let realTimeMap = null;
        let eventMarkers = [];

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            initializeMap();
            checkCriticalEvents();

            // Set up real-time updates every 30 seconds
            setInterval(updateRealTimeData, 30000);
        });

        // Initialize charts
        function initializeCharts() {
            // Event Type Distribution Chart
            const eventTypeData = <?= json_encode($eventTypes) ?>;
            const typeCtx = document.getElementById('eventTypeChart').getContext('2d');

            new Chart(typeCtx, {
                type: 'doughnut',
                data: {
                    labels: eventTypeData.map(item => item.type.charAt(0).toUpperCase() + item.type.slice(1)),
                    datasets: [{
                        data: eventTypeData.map(item => item.count),
                        backgroundColor: [
                            '#e74c3c', '#3498db', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Hourly Activity Chart
            const hourlyData = <?= json_encode($hourlyEvents) ?>;
            const hourlyCtx = document.getElementById('hourlyChart').getContext('2d');

            // Fill missing hours with 0
            const hours = Array.from({length: 24}, (_, i) => i);
            const hourlyValues = hours.map(hour => {
                const found = hourlyData.find(item => parseInt(item.hour) === hour);
                return found ? parseInt(found.count) : 0;
            });

            new Chart(hourlyCtx, {
                type: 'line',
                data: {
                    labels: hours.map(h => h + ':00'),
                    datasets: [{
                        label: 'Events',
                        data: hourlyValues,
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });
        }

        // Initialize map
        function initializeMap() {
            realTimeMap = L.map('realTimeMap').setView([-1.9403, 29.8739], 12);

            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(realTimeMap);

            loadEventMarkers();
        }

        // Load event markers on map
        function loadEventMarkers() {
            fetch('../api/v2/events?limit=50')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Clear existing markers
                        eventMarkers.forEach(marker => realTimeMap.removeLayer(marker));
                        eventMarkers = [];

                        // Add new markers
                        data.data.forEach(event => {
                            const icon = getEventIcon(event.type, event.severity_level);
                            const marker = L.marker([event.latitude, event.longitude], { icon })
                                .addTo(realTimeMap)
                                .bindPopup(createEventPopup(event));

                            eventMarkers.push(marker);
                        });
                    }
                })
                .catch(error => console.error('Error loading events:', error));
        }

        // Get custom icon for event type
        function getEventIcon(type, severity) {
            const colors = {
                'accident': '#e74c3c',
                'fire': '#fd7e14',
                'medical': '#e83e8c',
                'crime': '#6f42c1',
                'show': '#28a745',
                'party': '#17a2b8',
                'sports': '#ffc107'
            };

            const color = colors[type] || '#6c757d';

            return L.divIcon({
                html: `<div style="background-color: ${color}; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
                className: 'custom-marker',
                iconSize: [20, 20],
                iconAnchor: [10, 10]
            });
        }

        // Create event popup content
        function createEventPopup(event) {
            return `
                <div class="event-popup">
                    <h6>${event.title}</h6>
                    <p><strong>Type:</strong> ${event.type}</p>
                    <p><strong>Severity:</strong> ${event.severity_level}</p>
                    <p><strong>Reporter:</strong> ${event.reporter_name}</p>
                    <p><strong>Time:</strong> ${event.time_ago}</p>
                    ${event.views ? `<p><strong>Views:</strong> ${event.views}</p>` : ''}
                </div>
            `;
        }

        // Check for critical events
        function checkCriticalEvents() {
            fetch('../api/v2/events?severity=critical&limit=5')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data.length > 0) {
                        showCriticalAlerts(data.data);
                    }
                })
                .catch(error => console.error('Error checking critical events:', error));
        }

        // Show critical alerts
        function showCriticalAlerts(events) {
            const alertPanel = document.getElementById('alertPanel');
            const alertsContainer = document.getElementById('criticalAlerts');

            alertsContainer.innerHTML = events.map(event => `
                <div class="alert-item mb-2">
                    <strong>${event.title}</strong> - ${event.time_ago}
                    <br><small>${event.type} at ${event.latitude}, ${event.longitude}</small>
                </div>
            `).join('');

            alertPanel.classList.remove('d-none');
        }

        // Dismiss alerts
        function dismissAlerts() {
            document.getElementById('alertPanel').classList.add('d-none');
        }

        // Toggle auto refresh
        function toggleAutoRefresh() {
            const refreshText = document.getElementById('refreshText');
            const refreshIcon = document.getElementById('refreshIcon');

            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                refreshText.textContent = 'Auto Refresh: OFF';
                refreshIcon.classList.remove('fa-spin');
            } else {
                autoRefreshInterval = setInterval(refreshDashboard, 60000); // 1 minute
                refreshText.textContent = 'Auto Refresh: ON';
                refreshIcon.classList.add('fa-spin');
            }
        }

        // Refresh dashboard
        function refreshDashboard() {
            const spinner = document.getElementById('loadingSpinner');
            spinner.style.display = 'block';

            // Simulate loading time
            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        // Update real-time data
        function updateRealTimeData() {
            // Update event markers
            loadEventMarkers();

            // Check for new critical events
            checkCriticalEvents();

            // Update timeline
            updateEventTimeline();
        }

        // Update event timeline
        function updateEventTimeline() {
            fetch('../api/v2/events?limit=10')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const timeline = document.getElementById('eventTimeline');
                        timeline.innerHTML = data.data.map(event => `
                            <div class="timeline-item ${event.severity_level || 'medium'}">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">${event.title}</h6>
                                        <small class="text-muted">
                                            by ${event.reporter_name} • ${event.time_ago}
                                        </small>
                                    </div>
                                    <span class="badge bg-${event.type === 'accident' ? 'danger' : 'primary'}">
                                        ${event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                                    </span>
                                </div>
                            </div>
                        `).join('');
                    }
                })
                .catch(error => console.error('Error updating timeline:', error));
        }

        // WebSocket connection for real-time updates (if available)
        function initializeWebSocket() {
            if (typeof SockJS !== 'undefined') {
                const sock = new SockJS('/websocket');

                sock.onopen = function() {
                    console.log('WebSocket connected');
                };

                sock.onmessage = function(e) {
                    const data = JSON.parse(e.data);

                    if (data.type === 'new_event') {
                        // Add new event to map
                        const event = data.event;
                        const icon = getEventIcon(event.type, event.severity_level);
                        const marker = L.marker([event.latitude, event.longitude], { icon })
                            .addTo(realTimeMap)
                            .bindPopup(createEventPopup(event));

                        eventMarkers.push(marker);

                        // Show notification
                        showNotification(`New ${event.type}: ${event.title}`, 'info');
                    }
                };

                sock.onclose = function() {
                    console.log('WebSocket disconnected');
                    // Attempt to reconnect after 5 seconds
                    setTimeout(initializeWebSocket, 5000);
                };
            }
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }

        // Initialize WebSocket if available
        setTimeout(initializeWebSocket, 1000);

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'r':
                        e.preventDefault();
                        refreshDashboard();
                        break;
                    case 'a':
                        e.preventDefault();
                        toggleAutoRefresh();
                        break;
                }
            }
        });

        // Performance monitoring
        function monitorPerformance() {
            if ('performance' in window) {
                const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
                console.log(`Dashboard loaded in ${loadTime}ms`);

                // Send performance data to analytics (if enabled)
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'page_load_time', {
                        value: loadTime,
                        event_category: 'Performance'
                    });
                }
            }
        }

        // Monitor performance on load
        window.addEventListener('load', monitorPerformance);
    </script>
</body>
</html>
