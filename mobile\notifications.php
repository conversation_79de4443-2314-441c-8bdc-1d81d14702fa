<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require '../includes/db_connect.php';
require '../includes/auth_check.php';
require '../includes/functions.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#2c3e50">
    <title>Notifications - DOWN-RWANDA</title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            padding-bottom: 80px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .notifications-container {
            margin: 1rem;
        }

        .notification-item {
            background: white;
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .notification-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .notification-item.unread {
            border-left: 4px solid var(--secondary-color);
        }

        .notification-item.unread::before {
            content: '';
            position: absolute;
            top: 1rem;
            right: 1rem;
            width: 8px;
            height: 8px;
            background: var(--secondary-color);
            border-radius: 50%;
        }

        .notification-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 0.5rem;
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: white;
            flex-shrink: 0;
        }

        .notification-icon.info { background: var(--secondary-color); }
        .notification-icon.success { background: var(--success-color); }
        .notification-icon.warning { background: var(--warning-color); }
        .notification-icon.danger { background: var(--danger-color); }

        .notification-content {
            flex: 1;
        }

        .notification-message {
            font-weight: 500;
            margin-bottom: 0.5rem;
            line-height: 1.4;
        }

        .notification-time {
            font-size: 0.8rem;
            color: #6c757d;
        }

        .notification-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .action-btn {
            background: none;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            padding: 0.25rem 0.75rem;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: #f8f9fa;
        }

        .action-btn.primary {
            background: var(--secondary-color);
            color: white;
            border-color: var(--secondary-color);
        }

        .filter-tabs {
            background: white;
            border-radius: 15px;
            padding: 0.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: 0.5rem;
        }

        .filter-tab {
            flex: 1;
            background: none;
            border: none;
            border-radius: 10px;
            padding: 0.5rem;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .filter-tab.active {
            background: var(--secondary-color);
            color: white;
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .loading-spinner {
            text-align: center;
            padding: 2rem;
        }

        .floating-actions {
            position: fixed;
            bottom: 100px;
            right: 20px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            color: white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

        .floating-btn:hover {
            transform: scale(1.1);
        }

        .floating-btn.mark-all {
            background: linear-gradient(135deg, var(--success-color), #229954);
        }

        .floating-btn.clear-all {
            background: linear-gradient(135deg, var(--danger-color), #c0392b);
        }

        .notification-item.removing {
            animation: slideOut 0.3s ease-out forwards;
        }

        @keyframes slideOut {
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .swipe-actions {
            position: absolute;
            top: 0;
            right: 0;
            height: 100%;
            display: flex;
            align-items: center;
            background: var(--danger-color);
            color: white;
            padding: 0 1rem;
            border-radius: 0 15px 15px 0;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification-item.swiped .swipe-actions {
            transform: translateX(0);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <a href="index.php" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-arrow-left"></i> Back
                </a>
            </div>
            <h5 class="mb-0">Notifications</h5>
            <div>
                <span class="badge bg-primary" id="unreadCount">0</span>
            </div>
        </div>
    </div>

    <div class="notifications-container">
        <!-- Filter Tabs -->
        <div class="filter-tabs">
            <button class="filter-tab active" data-filter="all" onclick="setFilter('all')">All</button>
            <button class="filter-tab" data-filter="unread" onclick="setFilter('unread')">Unread</button>
            <button class="filter-tab" data-filter="info" onclick="setFilter('info')">Info</button>
            <button class="filter-tab" data-filter="warning" onclick="setFilter('warning')">Alerts</button>
        </div>

        <!-- Loading Spinner -->
        <div class="loading-spinner" id="loadingSpinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading notifications...</p>
        </div>

        <!-- Notifications List -->
        <div id="notificationsList"></div>

        <!-- Empty State -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="fas fa-bell-slash"></i>
            <h6>No notifications</h6>
            <p>You're all caught up!</p>
        </div>
    </div>

    <!-- Floating Actions -->
    <div class="floating-actions">
        <button class="floating-btn mark-all" onclick="markAllAsRead()" title="Mark all as read">
            <i class="fas fa-check"></i>
        </button>
        <button class="floating-btn clear-all" onclick="clearAllRead()" title="Clear read notifications">
            <i class="fas fa-trash"></i>
        </button>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let notifications = [];
        let currentFilter = 'all';
        let touchStartX = 0;
        let touchStartY = 0;

        // Load notifications on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadNotifications();

            // Auto-refresh every 30 seconds
            setInterval(loadNotifications, 30000);
        });

        async function loadNotifications() {
            try {
                const response = await fetch('../api/v2/notifications', {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`
                    }
                });

                const result = await response.json();

                if (result.success) {
                    notifications = result.data;
                    updateUnreadCount(result.unread_count);
                    renderNotifications();
                } else {
                    console.error('Failed to load notifications:', result.error);
                    showError('Failed to load notifications');
                }
            } catch (error) {
                console.error('Error loading notifications:', error);
                showError('Network error while loading notifications');
            } finally {
                document.getElementById('loadingSpinner').style.display = 'none';
            }
        }

        function renderNotifications() {
            const container = document.getElementById('notificationsList');
            const emptyState = document.getElementById('emptyState');

            // Filter notifications
            const filteredNotifications = notifications.filter(notification => {
                if (currentFilter === 'all') return true;
                if (currentFilter === 'unread') return !notification.is_read;
                return notification.type === currentFilter;
            });

            if (filteredNotifications.length === 0) {
                container.innerHTML = '';
                emptyState.style.display = 'block';
                return;
            }

            emptyState.style.display = 'none';

            container.innerHTML = filteredNotifications.map(notification => `
                <div class="notification-item ${!notification.is_read ? 'unread' : ''}"
                     data-id="${notification.id}"
                     ontouchstart="handleTouchStart(event)"
                     ontouchmove="handleTouchMove(event)"
                     ontouchend="handleTouchEnd(event)">

                    <div class="d-flex">
                        <div class="notification-icon ${notification.type}">
                            <i class="fas fa-${getNotificationIcon(notification.type)}"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-message">
                                ${notification.message}
                            </div>
                            <div class="notification-time">
                                ${formatTimeAgo(notification.created_at)}
                            </div>
                            ${!notification.is_read ? `
                                <div class="notification-actions">
                                    <button class="action-btn primary" onclick="markAsRead(${notification.id})">
                                        Mark as read
                                    </button>
                                </div>
                            ` : ''}
                        </div>
                    </div>

                    <div class="swipe-actions" onclick="deleteNotification(${notification.id})">
                        <i class="fas fa-trash"></i>
                    </div>
                </div>
            `).join('');
        }

        function setFilter(filter) {
            currentFilter = filter;

            // Update UI
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

            // Re-render notifications
            renderNotifications();
        }

        async function markAsRead(notificationId) {
            try {
                const response = await fetch(`../api/v2/notifications/${notificationId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`
                    },
                    body: JSON.stringify({ is_read: true })
                });

                const result = await response.json();

                if (result.success) {
                    // Update local data
                    const notification = notifications.find(n => n.id === notificationId);
                    if (notification) {
                        notification.is_read = true;
                    }

                    renderNotifications();
                    updateUnreadCount();
                    showToast('Marked as read', 'success');
                } else {
                    throw new Error(result.error || 'Failed to mark as read');
                }
            } catch (error) {
                console.error('Error marking as read:', error);
                showToast('Failed to mark as read', 'error');
            }
        }

        async function deleteNotification(notificationId) {
            if (!confirm('Delete this notification?')) return;

            try {
                const response = await fetch(`../api/v2/notifications/${notificationId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`
                    }
                });

                const result = await response.json();

                if (result.success) {
                    // Remove from local data
                    notifications = notifications.filter(n => n.id !== notificationId);

                    // Animate removal
                    const element = document.querySelector(`[data-id="${notificationId}"]`);
                    if (element) {
                        element.classList.add('removing');
                        setTimeout(() => {
                            renderNotifications();
                            updateUnreadCount();
                        }, 300);
                    }

                    showToast('Notification deleted', 'success');
                } else {
                    throw new Error(result.error || 'Failed to delete notification');
                }
            } catch (error) {
                console.error('Error deleting notification:', error);
                showToast('Failed to delete notification', 'error');
            }
        }

        async function markAllAsRead() {
            const unreadNotifications = notifications.filter(n => !n.is_read);

            if (unreadNotifications.length === 0) {
                showToast('No unread notifications', 'info');
                return;
            }

            try {
                // Mark all as read locally first for immediate feedback
                notifications.forEach(n => n.is_read = true);
                renderNotifications();
                updateUnreadCount();

                // Then sync with server
                for (const notification of unreadNotifications) {
                    await markAsRead(notification.id);
                }

                showToast('All notifications marked as read', 'success');
            } catch (error) {
                console.error('Error marking all as read:', error);
                showToast('Failed to mark all as read', 'error');
                // Reload to get correct state
                loadNotifications();
            }
        }

        async function clearAllRead() {
            const readNotifications = notifications.filter(n => n.is_read);

            if (readNotifications.length === 0) {
                showToast('No read notifications to clear', 'info');
                return;
            }

            if (!confirm(`Delete ${readNotifications.length} read notifications?`)) return;

            try {
                for (const notification of readNotifications) {
                    await deleteNotification(notification.id);
                }

                showToast('Read notifications cleared', 'success');
            } catch (error) {
                console.error('Error clearing read notifications:', error);
                showToast('Failed to clear notifications', 'error');
            }
        }

        function updateUnreadCount(count = null) {
            if (count === null) {
                count = notifications.filter(n => !n.is_read).length;
            }
            document.getElementById('unreadCount').textContent = count;
        }

        function getNotificationIcon(type) {
            const icons = {
                'info': 'info-circle',
                'success': 'check-circle',
                'warning': 'exclamation-triangle',
                'danger': 'exclamation-circle'
            };
            return icons[type] || 'bell';
        }

        function formatTimeAgo(datetime) {
            const now = new Date();
            const time = new Date(datetime);
            const diffInSeconds = Math.floor((now - time) / 1000);

            if (diffInSeconds < 60) return 'just now';
            if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' minutes ago';
            if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' hours ago';
            if (diffInSeconds < 2592000) return Math.floor(diffInSeconds / 86400) + ' days ago';

            return time.toLocaleDateString();
        }

        // Touch handling for swipe to delete
        function handleTouchStart(e) {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
        }

        function handleTouchMove(e) {
            if (!touchStartX || !touchStartY) return;

            const touchEndX = e.touches[0].clientX;
            const touchEndY = e.touches[0].clientY;

            const diffX = touchStartX - touchEndX;
            const diffY = touchStartY - touchEndY;

            // Only handle horizontal swipes
            if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                e.preventDefault();

                const element = e.currentTarget;
                if (diffX > 0) {
                    // Swipe left - show delete action
                    element.classList.add('swiped');
                } else {
                    // Swipe right - hide delete action
                    element.classList.remove('swiped');
                }
            }
        }

        function handleTouchEnd(e) {
            touchStartX = 0;
            touchStartY = 0;
        }

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 250px;';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(toast);

            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 5000);
        }

        function showError(message) {
            document.getElementById('loadingSpinner').innerHTML = `
                <div class="text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <p>${message}</p>
                    <button class="btn btn-primary btn-sm" onclick="location.reload()">Retry</button>
                </div>
            `;
        }
    </script>
</body>
</html>
