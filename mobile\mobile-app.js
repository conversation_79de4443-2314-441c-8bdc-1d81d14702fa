// Mobile App JavaScript for DOWN-RWANDA
class MobileApp {
    constructor() {
        this.currentLocation = null;
        this.map = null;
        this.isOnline = navigator.onLine;
        this.notificationPermission = 'default';
        this.serviceWorker = null;
        
        this.init();
    }
    
    async init() {
        // Initialize app
        this.setupEventListeners();
        this.initializeMap();
        this.requestNotificationPermission();
        this.registerServiceWorker();
        this.setupOfflineHandling();
        this.startLocationTracking();
        
        // Check for updates
        this.checkForUpdates();
        
        console.log('DOWN-RWANDA Mobile App initialized');
    }
    
    setupEventListeners() {
        // Online/offline status
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.showToast('Connection restored', 'success');
            this.syncOfflineData();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.showToast('You are offline', 'warning');
        });
        
        // Touch events for better mobile experience
        document.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
        document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: true });
        
        // Prevent zoom on double tap
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
        
        // Handle device orientation
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                if (this.map) {
                    this.map.invalidateSize();
                }
            }, 100);
        });
    }
    
    initializeMap() {
        if (document.getElementById('miniMap')) {
            this.map = L.map('miniMap', {
                zoomControl: false,
                attributionControl: false
            }).setView([-1.9403, 29.8739], 12);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                maxZoom: 18
            }).addTo(this.map);
            
            // Add current location marker when available
            if (this.currentLocation) {
                this.addCurrentLocationMarker();
            }
            
            this.loadNearbyEvents();
        }
    }
    
    async requestNotificationPermission() {
        if ('Notification' in window) {
            this.notificationPermission = await Notification.requestPermission();
            
            if (this.notificationPermission === 'granted') {
                this.showToast('Notifications enabled', 'success');
            }
        }
    }
    
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                this.serviceWorker = await navigator.serviceWorker.register('sw.js');
                console.log('Service Worker registered');
                
                // Listen for updates
                this.serviceWorker.addEventListener('updatefound', () => {
                    this.showUpdateAvailable();
                });
            } catch (error) {
                console.error('Service Worker registration failed:', error);
            }
        }
    }
    
    setupOfflineHandling() {
        // Cache important data for offline use
        this.cacheEssentialData();
        
        // Intercept form submissions for offline queueing
        document.addEventListener('submit', (e) => {
            if (!this.isOnline && e.target.classList.contains('offline-queue')) {
                e.preventDefault();
                this.queueOfflineAction(e.target);
            }
        });
    }
    
    startLocationTracking() {
        if ('geolocation' in navigator) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    this.currentLocation = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };
                    
                    this.updateLocationOnServer();
                    this.loadNearbyEvents();
                    
                    if (this.map) {
                        this.addCurrentLocationMarker();
                    }
                },
                (error) => {
                    console.error('Geolocation error:', error);
                    this.showToast('Location access denied', 'warning');
                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 300000 // 5 minutes
                }
            );
            
            // Watch position for continuous tracking
            navigator.geolocation.watchPosition(
                (position) => {
                    this.currentLocation = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };
                    this.updateLocationOnServer();
                },
                null,
                {
                    enableHighAccuracy: false,
                    timeout: 30000,
                    maximumAge: 600000 // 10 minutes
                }
            );
        }
    }
    
    async updateLocationOnServer() {
        if (!this.currentLocation || !this.isOnline) return;
        
        try {
            const response = await fetch('../api/v2/users/location', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify(this.currentLocation)
            });
            
            if (!response.ok) {
                throw new Error('Failed to update location');
            }
        } catch (error) {
            console.error('Location update failed:', error);
        }
    }
    
    addCurrentLocationMarker() {
        if (!this.map || !this.currentLocation) return;
        
        const currentLocationIcon = L.divIcon({
            html: '<div style="background: #007bff; width: 16px; height: 16px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>',
            className: 'current-location-marker',
            iconSize: [16, 16],
            iconAnchor: [8, 8]
        });
        
        L.marker([this.currentLocation.lat, this.currentLocation.lng], {
            icon: currentLocationIcon
        }).addTo(this.map).bindPopup('Your location');
        
        this.map.setView([this.currentLocation.lat, this.currentLocation.lng], 13);
    }
    
    async loadNearbyEvents() {
        if (!this.currentLocation || !this.isOnline) return;
        
        try {
            const response = await fetch(
                `../api/v2/events?lat=${this.currentLocation.lat}&lng=${this.currentLocation.lng}&radius=5&limit=20`,
                {
                    headers: {
                        'Authorization': `Bearer ${this.getAuthToken()}`
                    }
                }
            );
            
            const data = await response.json();
            
            if (data.success && this.map) {
                data.data.forEach(event => {
                    const marker = L.marker([event.latitude, event.longitude])
                        .addTo(this.map)
                        .bindPopup(`
                            <div class="event-popup">
                                <h6>${event.title}</h6>
                                <p><strong>Type:</strong> ${event.type}</p>
                                <p><strong>Distance:</strong> ${event.distance}km</p>
                                <button class="btn btn-sm btn-primary" onclick="viewEvent(${event.id})">
                                    View Details
                                </button>
                            </div>
                        `);
                });
            }
        } catch (error) {
            console.error('Failed to load nearby events:', error);
        }
    }
    
    async reportEmergency() {
        if (!this.currentLocation) {
            this.showToast('Location required for emergency report', 'error');
            this.requestLocation();
            return;
        }
        
        // Show emergency form
        const emergencyData = await this.showEmergencyForm();
        
        if (emergencyData) {
            try {
                const response = await fetch('../api/v2/events', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.getAuthToken()}`
                    },
                    body: JSON.stringify({
                        title: emergencyData.title,
                        type: emergencyData.type,
                        description: emergencyData.description,
                        latitude: this.currentLocation.lat,
                        longitude: this.currentLocation.lng,
                        severity_level: 'critical'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    this.showToast('Emergency reported successfully', 'success');
                    this.sendLocalNotification('Emergency Reported', 'Help is on the way');
                    
                    // Vibrate if supported
                    if ('vibrate' in navigator) {
                        navigator.vibrate([200, 100, 200]);
                    }
                } else {
                    throw new Error(result.error || 'Failed to report emergency');
                }
            } catch (error) {
                console.error('Emergency report failed:', error);
                this.showToast('Failed to report emergency', 'error');
                
                // Queue for offline submission
                if (!this.isOnline) {
                    this.queueOfflineAction({
                        type: 'emergency_report',
                        data: emergencyData
                    });
                }
            }
        }
    }
    
    showEmergencyForm() {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Emergency Report
                            </h5>
                        </div>
                        <div class="modal-body">
                            <form id="emergencyForm">
                                <div class="mb-3">
                                    <label class="form-label">Emergency Type</label>
                                    <select class="form-select" name="type" required>
                                        <option value="accident">Traffic Accident</option>
                                        <option value="fire">Fire Emergency</option>
                                        <option value="medical">Medical Emergency</option>
                                        <option value="crime">Crime in Progress</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Brief Description</label>
                                    <input type="text" class="form-control" name="title" 
                                           placeholder="What happened?" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Additional Details</label>
                                    <textarea class="form-control" name="description" rows="3"
                                              placeholder="Any additional information..."></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                Cancel
                            </button>
                            <button type="button" class="btn btn-danger" onclick="submitEmergency()">
                                <i class="fas fa-paper-plane me-2"></i>
                                Report Emergency
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
            
            window.submitEmergency = () => {
                const form = document.getElementById('emergencyForm');
                const formData = new FormData(form);
                
                resolve({
                    type: formData.get('type'),
                    title: formData.get('title'),
                    description: formData.get('description')
                });
                
                bootstrapModal.hide();
                modal.remove();
            };
            
            modal.addEventListener('hidden.bs.modal', () => {
                resolve(null);
                modal.remove();
            });
        });
    }
    
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 250px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    }
    
    sendLocalNotification(title, body) {
        if (this.notificationPermission === 'granted') {
            new Notification(title, {
                body: body,
                icon: '../assets/icons/icon-192x192.png',
                badge: '../assets/icons/icon-72x72.png',
                vibrate: [200, 100, 200]
            });
        }
    }
    
    getAuthToken() {
        return localStorage.getItem('auth_token') || '';
    }
    
    handleTouchStart(e) {
        this.touchStartX = e.touches[0].clientX;
        this.touchStartY = e.touches[0].clientY;
    }
    
    handleTouchMove(e) {
        if (!this.touchStartX || !this.touchStartY) return;
        
        const touchEndX = e.touches[0].clientX;
        const touchEndY = e.touches[0].clientY;
        
        const diffX = this.touchStartX - touchEndX;
        const diffY = this.touchStartY - touchEndY;
        
        // Implement swipe gestures if needed
        if (Math.abs(diffX) > Math.abs(diffY)) {
            if (diffX > 50) {
                // Swipe left
                this.handleSwipeLeft();
            } else if (diffX < -50) {
                // Swipe right
                this.handleSwipeRight();
            }
        }
    }
    
    handleSwipeLeft() {
        // Navigate to next section or perform action
    }
    
    handleSwipeRight() {
        // Navigate to previous section or perform action
    }
    
    cacheEssentialData() {
        // Cache user data and recent events for offline use
        if ('localStorage' in window) {
            // This would be implemented based on specific needs
        }
    }
    
    queueOfflineAction(action) {
        const offlineQueue = JSON.parse(localStorage.getItem('offline_queue') || '[]');
        offlineQueue.push({
            ...action,
            timestamp: Date.now()
        });
        localStorage.setItem('offline_queue', JSON.stringify(offlineQueue));
        
        this.showToast('Action queued for when online', 'info');
    }
    
    async syncOfflineData() {
        const offlineQueue = JSON.parse(localStorage.getItem('offline_queue') || '[]');
        
        if (offlineQueue.length === 0) return;
        
        for (const action of offlineQueue) {
            try {
                // Process queued action
                await this.processOfflineAction(action);
            } catch (error) {
                console.error('Failed to sync offline action:', error);
            }
        }
        
        localStorage.removeItem('offline_queue');
        this.showToast('Offline data synced', 'success');
    }
    
    async processOfflineAction(action) {
        // Process different types of offline actions
        switch (action.type) {
            case 'emergency_report':
                // Resubmit emergency report
                break;
            // Add other action types as needed
        }
    }
    
    checkForUpdates() {
        if (this.serviceWorker) {
            this.serviceWorker.addEventListener('updatefound', () => {
                this.showUpdateAvailable();
            });
        }
    }
    
    showUpdateAvailable() {
        const updateBanner = document.createElement('div');
        updateBanner.className = 'alert alert-info position-fixed';
        updateBanner.style.cssText = 'bottom: 80px; left: 20px; right: 20px; z-index: 9999;';
        updateBanner.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <span>App update available</span>
                <button class="btn btn-sm btn-primary" onclick="location.reload()">
                    Update
                </button>
            </div>
        `;
        
        document.body.appendChild(updateBanner);
    }
}

// Global functions for mobile interface
function requestLocation() {
    app.startLocationTracking();
}

function reportEmergency() {
    app.reportEmergency();
}

function viewEvent(eventId) {
    window.location.href = `event.php?id=${eventId}`;
}

function showMap() {
    window.location.href = 'map.php';
}

function toggleNotifications() {
    // Toggle notification settings
    app.requestNotificationPermission();
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new MobileApp();
});

// Export for module use if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobileApp;
}
