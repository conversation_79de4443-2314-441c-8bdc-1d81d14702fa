<?php
require '../includes/db_connect.php';

// Get new events from last 15 minutes
$events = $pdo->query("
    SELECT id, title, type, latitude, longitude 
    FROM events 
    WHERE created_at >= NOW() - INTERVAL 15 MINUTE
")->fetchAll();

foreach ($events as $event) {
    // Find users within 10km radius
    $stmt = $pdo->prepare("
        SELECT u.id, u.username, u.email
        FROM users u
        WHERE ST_Distance_Sphere(
            POINT(?, ?),
            u.last_location
        ) <= 10000
    ");
    $stmt->execute([$event['longitude'], $event['latitude']]);
    
    while ($user = $stmt->fetch()) {
        // Insert notification
        $pdo->prepare("
            INSERT INTO notifications 
            (user_id, message) 
            VALUES (?, ?)
        ")->execute([
            $user['id'],
            "New {$event['type']} event near you: {$event['title']}"
        ]);
        
        // For police and accidents
        if ($event['type'] === 'accident' && $user['role'] === 'police') {
            require '../includes/send_sms.php';
            sendSMS($user['phone'], 
                "URGENT: Accident reported at ".$event['latitude'].",".$event['longitude']
            );
        }
    }
}
?>