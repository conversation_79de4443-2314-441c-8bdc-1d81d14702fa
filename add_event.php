<?php
session_start();
require 'includes/db_connect.php';
require 'includes/auth_check.php';

// Only allow logged-in users
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate inputs
    $title = trim($_POST['title']);
    $latitude = floatval($_POST['latitude']);
    $longitude = floatval($_POST['longitude']);
    $image = $_FILES['event_image'];

    if (empty($title) || empty($latitude) || empty($longitude)) {
        die("All fields are required");
    }

    // Handle image upload
    $imagePath = null;
    if ($image['error'] === UPLOAD_ERR_OK) {
        $allowedTypes = ['image/jpeg', 'image/png'];
        if (!in_array($image['type'], $allowedTypes)) {
            die("Only JPG and PNG images are allowed");
        }

        $uploadDir = 'uploads/events/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $extension = pathinfo($image['name'], PATHINFO_EXTENSION);
        $filename = uniqid() . '.' . $extension;
        move_uploaded_file($image['tmp_name'], $uploadDir . $filename);
        $imagePath = $uploadDir . $filename;
    }

    // Insert event
    try {
        $stmt = $pdo->prepare("
            INSERT INTO events 
            (title, type, latitude, longitude, image_path, user_id) 
            VALUES (?, 'accident', ?, ?, ?, ?)
        ");
        $stmt->execute([$title, $latitude, $longitude, $imagePath, $_SESSION['user_id']]);
        
        header("Location: index.php?success=1");
        exit;
    } catch (PDOException $e) {
        die("Database error: " . $e->getMessage());
    }
}
?>