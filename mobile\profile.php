<?php
session_start();
require '../includes/db_connect.php';
require '../includes/auth_check.php';
require '../includes/functions.php';

// Get user profile data
$stmt = $pdo->prepare("
    SELECT u.*, 
           (SELECT COUNT(*) FROM events WHERE user_id = u.id) as event_count,
           (SELECT COUNT(*) FROM notifications WHERE user_id = u.id AND is_read = FALSE) as unread_notifications
    FROM users u 
    WHERE u.id = ?
");
$stmt->execute([$_SESSION['user_id']]);
$userProfile = $stmt->fetch();

// Get recent events
$recentEvents = $pdo->prepare("
    SELECT e.*, ea.views 
    FROM events e 
    LEFT JOIN event_analytics ea ON e.id = ea.event_id 
    WHERE e.user_id = ? 
    ORDER BY e.created_at DESC 
    LIMIT 5
");
$recentEvents->execute([$_SESSION['user_id']]);
$events = $recentEvents->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#2c3e50">
    <title>Profile - DOWN-RWANDA</title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            padding-bottom: 80px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .profile-card {
            background: white;
            border-radius: 20px;
            margin: 1rem;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            border: 4px solid white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            margin-bottom: 1rem;
            object-fit: cover;
            cursor: pointer;
        }
        
        .avatar-placeholder {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin: 0 auto 1rem;
            cursor: pointer;
        }
        
        .stats-row {
            display: flex;
            justify-content: space-around;
            margin: 2rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 15px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .section-card {
            background: white;
            border-radius: 20px;
            margin: 1rem;
            padding: 1.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .event-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 15px;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .event-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }
        
        .event-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: white;
        }
        
        .event-icon.accident { background: var(--danger-color); }
        .event-icon.show { background: var(--success-color); }
        .event-icon.party { background: var(--secondary-color); }
        .event-icon.fire { background: #fd7e14; }
        .event-icon.medical { background: #e83e8c; }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 15px;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
        }
        
        .menu-item:hover {
            background: #e9ecef;
            color: inherit;
            text-decoration: none;
            transform: translateX(5px);
        }
        
        .menu-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            background: var(--secondary-color);
            color: white;
        }
        
        .role-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .role-user { background: #e3f2fd; color: #1976d2; }
        .role-police { background: #fff3e0; color: #f57c00; }
        .role-organization { background: #e8f5e8; color: #388e3c; }
        .role-admin { background: #fce4ec; color: #c2185b; }
        
        .edit-btn {
            background: linear-gradient(135deg, var(--secondary-color), #2980b9);
            border: none;
            border-radius: 25px;
            padding: 0.5rem 1.5rem;
            color: white;
            font-weight: 500;
            margin-top: 1rem;
        }
        
        .logout-btn {
            background: linear-gradient(135deg, var(--danger-color), #c0392b);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 2rem;
            color: white;
            font-weight: 600;
            width: 100%;
            margin-top: 2rem;
        }
        
        .modal-content {
            border-radius: 20px;
            border: none;
        }
        
        .modal-header {
            border-bottom: none;
            padding-bottom: 0;
        }
        
        .form-control {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
        }
        
        .form-control:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        
        .avatar-upload {
            position: relative;
            display: inline-block;
        }
        
        .avatar-upload input[type="file"] {
            display: none;
        }
        
        .avatar-upload-overlay {
            position: absolute;
            bottom: 0;
            right: 0;
            background: var(--secondary-color);
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <a href="index.php" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-arrow-left"></i> Back
                </a>
            </div>
            <h5 class="mb-0">Profile</h5>
            <div>
                <button class="btn btn-outline-secondary btn-sm" onclick="showEditProfile()">
                    <i class="fas fa-edit"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Profile Card -->
    <div class="profile-card">
        <div class="avatar-upload">
            <?php if ($userProfile['avatar']): ?>
                <img src="../uploads/avatars/<?= htmlspecialchars($userProfile['avatar']) ?>" 
                     alt="Avatar" class="avatar" onclick="document.getElementById('avatarInput').click()">
            <?php else: ?>
                <div class="avatar-placeholder" onclick="document.getElementById('avatarInput').click()">
                    <?= strtoupper(substr($userProfile['username'], 0, 1)) ?>
                </div>
            <?php endif; ?>
            <div class="avatar-upload-overlay" onclick="document.getElementById('avatarInput').click()">
                <i class="fas fa-camera"></i>
            </div>
            <input type="file" id="avatarInput" accept="image/*" onchange="uploadAvatar(this)">
        </div>
        
        <h4><?= htmlspecialchars($userProfile['username']) ?></h4>
        <p class="text-muted mb-2"><?= htmlspecialchars($userProfile['email']) ?></p>
        <span class="role-badge role-<?= $userProfile['role'] ?>">
            <?= ucfirst($userProfile['role']) ?>
        </span>
        
        <div class="stats-row">
            <div class="stat-item">
                <div class="stat-number"><?= $userProfile['event_count'] ?></div>
                <div class="stat-label">Events</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= $userProfile['unread_notifications'] ?></div>
                <div class="stat-label">Notifications</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= date('M Y', strtotime($userProfile['created_at'])) ?></div>
                <div class="stat-label">Joined</div>
            </div>
        </div>
        
        <button class="edit-btn" onclick="showEditProfile()">
            <i class="fas fa-edit me-2"></i>Edit Profile
        </button>
    </div>

    <!-- Recent Events -->
    <?php if (!empty($events)): ?>
    <div class="section-card">
        <h6 class="section-title">
            <i class="fas fa-history"></i>
            Recent Events
        </h6>
        <?php foreach ($events as $event): ?>
        <div class="event-item" onclick="viewEvent(<?= $event['id'] ?>)">
            <div class="event-icon <?= $event['type'] ?>">
                <i class="fas fa-<?= getEventIcon($event['type']) ?>"></i>
            </div>
            <div class="flex-grow-1">
                <div class="fw-bold"><?= htmlspecialchars($event['title']) ?></div>
                <small class="text-muted">
                    <?= formatTimeAgo($event['created_at']) ?>
                    <?php if ($event['views']): ?>
                    • <?= $event['views'] ?> views
                    <?php endif; ?>
                </small>
            </div>
            <i class="fas fa-chevron-right text-muted"></i>
        </div>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>

    <!-- Menu -->
    <div class="section-card">
        <h6 class="section-title">
            <i class="fas fa-cog"></i>
            Settings
        </h6>
        
        <a href="notifications.php" class="menu-item">
            <div class="menu-icon">
                <i class="fas fa-bell"></i>
            </div>
            <div class="flex-grow-1">
                <div class="fw-bold">Notifications</div>
                <small class="text-muted">Manage your alerts</small>
            </div>
            <i class="fas fa-chevron-right text-muted"></i>
        </a>
        
        <a href="#" class="menu-item" onclick="showChangePassword()">
            <div class="menu-icon">
                <i class="fas fa-lock"></i>
            </div>
            <div class="flex-grow-1">
                <div class="fw-bold">Change Password</div>
                <small class="text-muted">Update your password</small>
            </div>
            <i class="fas fa-chevron-right text-muted"></i>
        </a>
        
        <a href="#" class="menu-item" onclick="showPrivacySettings()">
            <div class="menu-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div class="flex-grow-1">
                <div class="fw-bold">Privacy</div>
                <small class="text-muted">Privacy settings</small>
            </div>
            <i class="fas fa-chevron-right text-muted"></i>
        </a>
        
        <a href="#" class="menu-item" onclick="showAbout()">
            <div class="menu-icon">
                <i class="fas fa-info-circle"></i>
            </div>
            <div class="flex-grow-1">
                <div class="fw-bold">About</div>
                <small class="text-muted">App information</small>
            </div>
            <i class="fas fa-chevron-right text-muted"></i>
        </a>
        
        <button class="logout-btn" onclick="logout()">
            <i class="fas fa-sign-out-alt me-2"></i>Logout
        </button>
    </div>

    <!-- Edit Profile Modal -->
    <div class="modal fade" id="editProfileModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Profile</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editProfileForm">
                        <div class="mb-3">
                            <label class="form-label">Email</label>
                            <input type="email" class="form-control" id="editEmail" 
                                   value="<?= htmlspecialchars($userProfile['email']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Phone</label>
                            <input type="tel" class="form-control" id="editPhone" 
                                   value="<?= htmlspecialchars($userProfile['phone']) ?>">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveProfile()">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Change Password Modal -->
    <div class="modal fade" id="changePasswordModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Change Password</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="changePasswordForm">
                        <div class="mb-3">
                            <label class="form-label">Current Password</label>
                            <input type="password" class="form-control" id="currentPassword" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">New Password</label>
                            <input type="password" class="form-control" id="newPassword" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control" id="confirmPassword" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="changePassword()">Change Password</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showEditProfile() {
            new bootstrap.Modal(document.getElementById('editProfileModal')).show();
        }
        
        function showChangePassword() {
            new bootstrap.Modal(document.getElementById('changePasswordModal')).show();
        }
        
        function showPrivacySettings() {
            alert('Privacy settings coming soon!');
        }
        
        function showAbout() {
            alert('DOWN-RWANDA v2.0\nAdvanced Emergency Response System\n\nDeveloped for safer communities in Rwanda.');
        }
        
        async function saveProfile() {
            const email = document.getElementById('editEmail').value;
            const phone = document.getElementById('editPhone').value;
            
            try {
                const response = await fetch('../api/v2/users/profile', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`
                    },
                    body: JSON.stringify({ email, phone })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    bootstrap.Modal.getInstance(document.getElementById('editProfileModal')).hide();
                    showToast('Profile updated successfully', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    throw new Error(result.error || 'Failed to update profile');
                }
            } catch (error) {
                showToast('Failed to update profile: ' + error.message, 'error');
            }
        }
        
        async function changePassword() {
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (newPassword !== confirmPassword) {
                showToast('Passwords do not match', 'error');
                return;
            }
            
            if (newPassword.length < 6) {
                showToast('Password must be at least 6 characters', 'error');
                return;
            }
            
            try {
                const response = await fetch('../api/v2/users/profile', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`
                    },
                    body: JSON.stringify({ password: newPassword })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    bootstrap.Modal.getInstance(document.getElementById('changePasswordModal')).hide();
                    showToast('Password changed successfully', 'success');
                    document.getElementById('changePasswordForm').reset();
                } else {
                    throw new Error(result.error || 'Failed to change password');
                }
            } catch (error) {
                showToast('Failed to change password: ' + error.message, 'error');
            }
        }
        
        async function uploadAvatar(input) {
            if (!input.files || !input.files[0]) return;
            
            const formData = new FormData();
            formData.append('avatar', input.files[0]);
            formData.append('type', 'avatar');
            
            try {
                const response = await fetch('../api/v2/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`
                    },
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showToast('Avatar updated successfully', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    throw new Error(result.error || 'Failed to upload avatar');
                }
            } catch (error) {
                showToast('Failed to upload avatar: ' + error.message, 'error');
            }
        }
        
        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                localStorage.removeItem('auth_token');
                window.location.href = '../logout.php';
            }
        }
        
        function viewEvent(eventId) {
            window.location.href = `event.php?id=${eventId}`;
        }
        
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 250px;';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 5000);
        }
    </script>
</body>
</html>

<?php
function getEventIcon($type) {
    $icons = [
        'accident' => 'car-crash',
        'fire' => 'fire',
        'medical' => 'ambulance',
        'crime' => 'shield-alt',
        'show' => 'music',
        'party' => 'glass-cheers',
        'sports' => 'futbol'
    ];
    return $icons[$type] ?? 'calendar-alt';
}
?>
