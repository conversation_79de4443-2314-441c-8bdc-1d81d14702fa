<?php
session_start();
require '../includes/db_connect.php';
require '../includes/auth_check.php';

if ($_SESSION['role'] !== 'organization') {
    header("HTTP/1.1 403 Forbidden");
    exit;
}

// Get organization's events
$events = $pdo->prepare("
    SELECT * FROM events 
    WHERE user_id = ? 
    ORDER BY created_at DESC
");
$events->execute([$_SESSION['user_id']]);
?>
<!DOCTYPE html>
<html>
<head>
    <title>Organization Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Organization Dashboard</h2>
        
        <div class="card mt-4">
            <div class="card-header">
                <h4>Your Events</h4>
            </div>
            <div class="card-body">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while($event = $events->fetch()): ?>
                        <tr class="<?= $event['is_paid'] ? 'paid-event' : 'accident-event' ?>">
                            <td><?= htmlspecialchars($event['title']) ?></td>
                            <td><?= ucfirst($event['type']) ?></td>
                            <td>
                                <?php if($event['is_approved']): ?>
                                    <span class="badge bg-success">Approved</span>
                                <?php else: ?>
                                    <span class="badge bg-warning">Pending</span>
                                <?php endif; ?>
                            </td>
                            <td><?= date('M d, Y', strtotime($event['created_at'])) ?></td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h4>Event Statistics</h4>
            </div>
            <div class="card-body">
                <canvas id="eventChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Simple chart showing event types
        const ctx = document.getElementById('eventChart').getContext('2d');
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['Accidents', 'Shows', 'Parties'],
                datasets: [{
                    label: 'Your Events',
                    data: [
                        <?= $events->rowCount("WHERE type = 'accident'") ?>,
                        <?= $events->rowCount("WHERE type = 'show'") ?>,
                        <?= $events->rowCount("WHERE type = 'party'") ?>
                    ],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)'
                    ]
                }]
            }
        });
    </script>
</body>
</html>