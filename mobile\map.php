<?php
session_start();
require '../includes/db_connect.php';
require '../includes/auth_check.php';
require '../includes/functions.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#2c3e50">
    <title>Live Map - DOWN-RWANDA</title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/leaflet@1.9.4/dist/leaflet.css" rel="stylesheet">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        #map {
            height: 100vh;
            width: 100%;
        }
        
        .map-controls {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            gap: 10px;
        }
        
        .control-btn {
            background: rgba(255, 255, 255, 0.95);
            border: none;
            border-radius: 25px;
            padding: 10px 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            font-size: 14px;
            font-weight: 500;
        }
        
        .filter-panel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            z-index: 1000;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }
        
        .filter-panel.show {
            transform: translateY(0);
        }
        
        .filter-chips {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-top: 10px;
        }
        
        .filter-chip {
            background: #e9ecef;
            border: none;
            border-radius: 20px;
            padding: 5px 12px;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .filter-chip.active {
            background: #007bff;
            color: white;
        }
        
        .floating-btn {
            position: absolute;
            bottom: 100px;
            right: 20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
            z-index: 1000;
            font-size: 1.2rem;
        }
        
        .event-popup {
            max-width: 250px;
        }
        
        .event-popup h6 {
            margin-bottom: 8px;
            color: #2c3e50;
        }
        
        .event-popup .badge {
            font-size: 10px;
        }
        
        .legend {
            position: absolute;
            top: 80px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            z-index: 1000;
            font-size: 12px;
            max-width: 150px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            flex-direction: column;
        }
        
        .loading-overlay.hidden {
            display: none;
        }
        
        .stats-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            z-index: 1000;
            font-size: 12px;
            min-width: 120px;
            text-align: center;
        }
        
        @media (max-width: 576px) {
            .map-controls {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .control-btn {
                font-size: 12px;
                padding: 8px 12px;
            }
            
            .legend {
                top: 120px;
                right: 10px;
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner-border text-primary mb-3" role="status"></div>
        <p>Loading events...</p>
    </div>

    <!-- Map Controls -->
    <div class="map-controls">
        <button class="control-btn" onclick="goBack()">
            <i class="fas fa-arrow-left me-2"></i>Back
        </button>
        <button class="control-btn" onclick="toggleFilters()">
            <i class="fas fa-filter me-2"></i>Filters
        </button>
        <button class="control-btn" onclick="refreshMap()">
            <i class="fas fa-sync-alt me-2"></i>Refresh
        </button>
    </div>

    <!-- Stats Panel -->
    <div class="stats-panel">
        <div class="fw-bold">Live Events</div>
        <div id="eventCount">Loading...</div>
    </div>

    <!-- Map -->
    <div id="map"></div>

    <!-- My Location Button -->
    <button class="floating-btn" onclick="goToMyLocation()" title="My Location">
        <i class="fas fa-location-arrow"></i>
    </button>

    <!-- Filter Panel -->
    <div class="filter-panel" id="filterPanel">
        <div class="d-flex justify-content-between align-items-center">
            <h6 class="mb-0">Filter Events</h6>
            <button class="btn-close" onclick="toggleFilters()"></button>
        </div>
        
        <div class="filter-chips">
            <button class="filter-chip active" data-filter="all" onclick="setFilter('all')">All</button>
            <button class="filter-chip" data-filter="accident" onclick="setFilter('accident')">Accidents</button>
            <button class="filter-chip" data-filter="fire" onclick="setFilter('fire')">Fire</button>
            <button class="filter-chip" data-filter="medical" onclick="setFilter('medical')">Medical</button>
            <button class="filter-chip" data-filter="crime" onclick="setFilter('crime')">Crime</button>
            <button class="filter-chip" data-filter="critical" onclick="setFilter('critical')">Critical</button>
        </div>
        
        <div class="mt-3">
            <label class="form-label">Radius (km)</label>
            <input type="range" class="form-range" id="radiusSlider" min="1" max="50" value="10" onchange="updateRadius(this.value)">
            <div class="d-flex justify-content-between">
                <small>1km</small>
                <small id="radiusValue">10km</small>
                <small>50km</small>
            </div>
        </div>
    </div>

    <!-- Legend -->
    <div class="legend">
        <div class="fw-bold mb-2">Legend</div>
        <div class="legend-item">
            <div class="legend-color" style="background: #dc3545;"></div>
            <span>Accident</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #fd7e14;"></div>
            <span>Fire</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #e83e8c;"></div>
            <span>Medical</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #6f42c1;"></div>
            <span>Crime</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #28a745;"></div>
            <span>Event</span>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <script>
        let map;
        let markers = [];
        let currentLocation = null;
        let currentFilter = 'all';
        let currentRadius = 10;
        let events = [];
        
        // Initialize map
        function initMap() {
            map = L.map('map').setView([-1.9403, 29.8739], 12);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 18
            }).addTo(map);
            
            // Get user location
            getCurrentLocation();
            
            // Load events
            loadEvents();
            
            // Auto-refresh every 30 seconds
            setInterval(loadEvents, 30000);
        }
        
        function getCurrentLocation() {
            if ('geolocation' in navigator) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        currentLocation = {
                            lat: position.coords.latitude,
                            lng: position.coords.longitude
                        };
                        
                        // Add current location marker
                        const currentLocationIcon = L.divIcon({
                            html: '<div style="background: #007bff; width: 16px; height: 16px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>',
                            className: 'current-location-marker',
                            iconSize: [16, 16],
                            iconAnchor: [8, 8]
                        });
                        
                        L.marker([currentLocation.lat, currentLocation.lng], {
                            icon: currentLocationIcon
                        }).addTo(map).bindPopup('Your location');
                        
                        // Center map on user location
                        map.setView([currentLocation.lat, currentLocation.lng], 14);
                        
                        // Load nearby events
                        loadEvents();
                    },
                    (error) => {
                        console.error('Geolocation error:', error);
                    }
                );
            }
        }
        
        async function loadEvents() {
            try {
                let url = '../api/v2/events?limit=100';
                
                if (currentLocation) {
                    url += `&lat=${currentLocation.lat}&lng=${currentLocation.lng}&radius=${currentRadius}`;
                }
                
                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    events = result.data;
                    updateMap();
                    updateEventCount();
                } else {
                    console.error('Failed to load events:', result.error);
                }
            } catch (error) {
                console.error('Error loading events:', error);
            } finally {
                document.getElementById('loadingOverlay').classList.add('hidden');
            }
        }
        
        function updateMap() {
            // Clear existing markers
            markers.forEach(marker => map.removeLayer(marker));
            markers = [];
            
            // Filter events
            const filteredEvents = events.filter(event => {
                if (currentFilter === 'all') return true;
                if (currentFilter === 'critical') return event.severity_level === 'critical';
                return event.type === currentFilter;
            });
            
            // Add event markers
            filteredEvents.forEach(event => {
                const marker = createEventMarker(event);
                markers.push(marker);
                marker.addTo(map);
            });
        }
        
        function createEventMarker(event) {
            const colors = {
                'accident': '#dc3545',
                'fire': '#fd7e14',
                'medical': '#e83e8c',
                'crime': '#6f42c1',
                'show': '#28a745',
                'party': '#17a2b8',
                'sports': '#ffc107'
            };
            
            const color = colors[event.type] || '#6c757d';
            const size = event.severity_level === 'critical' ? 24 : 20;
            
            const icon = L.divIcon({
                html: `<div style="background-color: ${color}; width: ${size}px; height: ${size}px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3); display: flex; align-items: center; justify-content: center; color: white; font-size: 10px;">
                    ${event.severity_level === 'critical' ? '!' : ''}
                </div>`,
                className: 'event-marker',
                iconSize: [size, size],
                iconAnchor: [size/2, size/2]
            });
            
            const marker = L.marker([event.latitude, event.longitude], { icon });
            
            const popupContent = `
                <div class="event-popup">
                    <h6>${event.title}</h6>
                    <p class="mb-2">${event.description || 'No description available'}</p>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="badge bg-${event.type === 'accident' ? 'danger' : 'primary'}">${event.type}</span>
                        <span class="badge bg-${event.severity_level === 'critical' ? 'danger' : event.severity_level === 'high' ? 'warning' : 'secondary'}">${event.severity_level}</span>
                    </div>
                    <small class="text-muted">
                        ${event.time_ago} • by ${event.reporter_name}
                        ${event.distance ? `• ${event.distance}km away` : ''}
                    </small>
                    ${event.views ? `<br><small class="text-muted">${event.views} views</small>` : ''}
                </div>
            `;
            
            marker.bindPopup(popupContent);
            
            return marker;
        }
        
        function updateEventCount() {
            const filteredCount = events.filter(event => {
                if (currentFilter === 'all') return true;
                if (currentFilter === 'critical') return event.severity_level === 'critical';
                return event.type === currentFilter;
            }).length;
            
            document.getElementById('eventCount').textContent = `${filteredCount} events`;
        }
        
        function setFilter(filter) {
            currentFilter = filter;
            
            // Update UI
            document.querySelectorAll('.filter-chip').forEach(chip => {
                chip.classList.remove('active');
            });
            document.querySelector(`[data-filter="${filter}"]`).classList.add('active');
            
            // Update map
            updateMap();
            updateEventCount();
        }
        
        function updateRadius(value) {
            currentRadius = parseInt(value);
            document.getElementById('radiusValue').textContent = value + 'km';
            
            // Reload events with new radius
            if (currentLocation) {
                loadEvents();
            }
        }
        
        function toggleFilters() {
            const panel = document.getElementById('filterPanel');
            panel.classList.toggle('show');
        }
        
        function refreshMap() {
            document.getElementById('loadingOverlay').classList.remove('hidden');
            loadEvents();
        }
        
        function goToMyLocation() {
            if (currentLocation) {
                map.setView([currentLocation.lat, currentLocation.lng], 16);
            } else {
                getCurrentLocation();
            }
        }
        
        function goBack() {
            window.history.back();
        }
        
        // Initialize map when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
        });
        
        // Handle orientation change
        window.addEventListener('orientationchange', function() {
            setTimeout(() => {
                if (map) {
                    map.invalidateSize();
                }
            }, 100);
        });
        
        // Close filter panel when clicking outside
        document.addEventListener('click', function(e) {
            const panel = document.getElementById('filterPanel');
            const filterBtn = document.querySelector('[onclick="toggleFilters()"]');
            
            if (!panel.contains(e.target) && !filterBtn.contains(e.target)) {
                panel.classList.remove('show');
            }
        });
    </script>
</body>
</html>
