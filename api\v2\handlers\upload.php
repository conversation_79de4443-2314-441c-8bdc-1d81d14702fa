<?php

function handleUpload($method, $user) {
    if ($method !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        return;
    }
    
    $uploadType = $_POST['type'] ?? 'general';
    
    switch ($uploadType) {
        case 'event_image':
            uploadEventImage($user);
            break;
        case 'avatar':
            uploadAvatar($user);
            break;
        case 'payment_proof':
            uploadPaymentProof($user);
            break;
        case 'evidence':
            uploadEvidence($user);
            break;
        default:
            uploadGeneral($user);
    }
}

function uploadEventImage($user) {
    global $pdo;
    
    if (!isset($_FILES['image']) || !isset($_POST['event_id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Image file and event ID required']);
        return;
    }
    
    $eventId = intval($_POST['event_id']);
    $imageType = sanitizeInput($_POST['image_type'] ?? 'main');
    
    // Verify event ownership or admin access
    $stmt = $pdo->prepare("SELECT user_id FROM events WHERE id = ?");
    $stmt->execute([$eventId]);
    $event = $stmt->fetch();
    
    if (!$event || ($event['user_id'] != $user['user_id'] && $user['role'] !== 'admin')) {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        return;
    }
    
    try {
        $filename = uploadFile($_FILES['image'], UPLOAD_DIR . 'events/');
        
        if (!$filename) {
            http_response_code(400);
            echo json_encode(['error' => 'Failed to upload image']);
            return;
        }
        
        // Generate thumbnail
        $thumbnailName = generateThumbnail(UPLOAD_DIR . 'events/' . $filename);
        
        // AI analysis if enabled
        $aiAnalysis = null;
        if (AI_IMAGE_ANALYSIS) {
            $aiAnalysis = analyzeImageSeverity(UPLOAD_DIR . 'events/' . $filename);
        }
        
        // Save to database
        $stmt = $pdo->prepare("
            INSERT INTO event_images (event_id, image_path, image_type, ai_analysis) 
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([
            $eventId, 
            $filename, 
            $imageType, 
            $aiAnalysis ? json_encode($aiAnalysis) : null
        ]);
        
        // Compress image for web
        compressImage(UPLOAD_DIR . 'events/' . $filename);
        
        echo json_encode([
            'success' => true,
            'data' => [
                'image_id' => $pdo->lastInsertId(),
                'filename' => $filename,
                'thumbnail' => $thumbnailName,
                'ai_analysis' => $aiAnalysis,
                'url' => BASE_URL . '/uploads/events/' . $filename
            ]
        ]);
        
        logActivity("Event image uploaded", 'INFO', [
            'event_id' => $eventId,
            'filename' => $filename,
            'user_id' => $user['user_id']
        ]);
        
    } catch (Exception $e) {
        logError("Failed to upload event image: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to process image upload']);
    }
}

function uploadAvatar($user) {
    global $pdo;
    
    if (!isset($_FILES['avatar'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Avatar file required']);
        return;
    }
    
    try {
        // Delete old avatar if exists
        $stmt = $pdo->prepare("SELECT avatar FROM users WHERE id = ?");
        $stmt->execute([$user['user_id']]);
        $oldAvatar = $stmt->fetchColumn();
        
        if ($oldAvatar && file_exists(UPLOAD_DIR . 'avatars/' . $oldAvatar)) {
            unlink(UPLOAD_DIR . 'avatars/' . $oldAvatar);
        }
        
        $filename = uploadFile($_FILES['avatar'], UPLOAD_DIR . 'avatars/', ['image/jpeg', 'image/png', 'image/jpg']);
        
        if (!$filename) {
            http_response_code(400);
            echo json_encode(['error' => 'Failed to upload avatar']);
            return;
        }
        
        // Generate thumbnail
        $thumbnailName = generateThumbnail(UPLOAD_DIR . 'avatars/' . $filename, 150, 150);
        
        // Compress image
        compressImage(UPLOAD_DIR . 'avatars/' . $filename);
        
        // Update user record
        $stmt = $pdo->prepare("UPDATE users SET avatar = ? WHERE id = ?");
        $stmt->execute([$filename, $user['user_id']]);
        
        echo json_encode([
            'success' => true,
            'data' => [
                'filename' => $filename,
                'thumbnail' => $thumbnailName,
                'url' => BASE_URL . '/uploads/avatars/' . $filename
            ]
        ]);
        
        logActivity("Avatar uploaded", 'INFO', [
            'filename' => $filename,
            'user_id' => $user['user_id']
        ]);
        
    } catch (Exception $e) {
        logError("Failed to upload avatar: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to process avatar upload']);
    }
}

function uploadPaymentProof($user) {
    global $pdo;
    
    if (!isset($_FILES['payment_proof']) || !isset($_POST['event_id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Payment proof file and event ID required']);
        return;
    }
    
    $eventId = intval($_POST['event_id']);
    
    // Verify event ownership
    $stmt = $pdo->prepare("SELECT user_id, is_paid FROM events WHERE id = ?");
    $stmt->execute([$eventId]);
    $event = $stmt->fetch();
    
    if (!$event || $event['user_id'] != $user['user_id']) {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        return;
    }
    
    if ($event['is_paid']) {
        http_response_code(400);
        echo json_encode(['error' => 'Payment proof already uploaded']);
        return;
    }
    
    try {
        $allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
        $filename = uploadFile($_FILES['payment_proof'], UPLOAD_DIR . 'payments/', $allowedTypes);
        
        if (!$filename) {
            http_response_code(400);
            echo json_encode(['error' => 'Failed to upload payment proof']);
            return;
        }
        
        // Update event record
        $stmt = $pdo->prepare("UPDATE events SET payment_proof = ?, is_paid = TRUE WHERE id = ?");
        $stmt->execute([$filename, $eventId]);
        
        echo json_encode([
            'success' => true,
            'data' => [
                'filename' => $filename,
                'url' => BASE_URL . '/uploads/payments/' . $filename
            ]
        ]);
        
        logActivity("Payment proof uploaded", 'INFO', [
            'event_id' => $eventId,
            'filename' => $filename,
            'user_id' => $user['user_id']
        ]);
        
    } catch (Exception $e) {
        logError("Failed to upload payment proof: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to process payment proof upload']);
    }
}

function uploadEvidence($user) {
    global $pdo;
    
    // Only emergency services can upload evidence
    if (!in_array($user['role'], ['admin', 'police', 'medical', 'fire_dept'])) {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        return;
    }
    
    if (!isset($_FILES['evidence']) || !isset($_POST['event_id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Evidence file and event ID required']);
        return;
    }
    
    $eventId = intval($_POST['event_id']);
    $description = sanitizeInput($_POST['description'] ?? '');
    
    try {
        $filename = uploadFile($_FILES['evidence'], UPLOAD_DIR . 'evidence/');
        
        if (!$filename) {
            http_response_code(400);
            echo json_encode(['error' => 'Failed to upload evidence']);
            return;
        }
        
        // AI analysis for evidence images
        $aiAnalysis = null;
        if (AI_IMAGE_ANALYSIS && in_array($_FILES['evidence']['type'], ['image/jpeg', 'image/png', 'image/jpg'])) {
            $aiAnalysis = analyzeImageSeverity(UPLOAD_DIR . 'evidence/' . $filename);
        }
        
        // Save to database
        $stmt = $pdo->prepare("
            INSERT INTO event_images (event_id, image_path, image_type, ai_analysis) 
            VALUES (?, ?, 'evidence', ?)
        ");
        $stmt->execute([
            $eventId, 
            $filename, 
            $aiAnalysis ? json_encode($aiAnalysis) : null
        ]);
        
        echo json_encode([
            'success' => true,
            'data' => [
                'evidence_id' => $pdo->lastInsertId(),
                'filename' => $filename,
                'ai_analysis' => $aiAnalysis,
                'url' => BASE_URL . '/uploads/evidence/' . $filename
            ]
        ]);
        
        logActivity("Evidence uploaded", 'INFO', [
            'event_id' => $eventId,
            'filename' => $filename,
            'user_id' => $user['user_id']
        ]);
        
    } catch (Exception $e) {
        logError("Failed to upload evidence: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to process evidence upload']);
    }
}

function uploadGeneral($user) {
    if (!isset($_FILES['file'])) {
        http_response_code(400);
        echo json_encode(['error' => 'File required']);
        return;
    }
    
    try {
        $filename = uploadFile($_FILES['file'], UPLOAD_DIR);
        
        if (!$filename) {
            http_response_code(400);
            echo json_encode(['error' => 'Failed to upload file']);
            return;
        }
        
        echo json_encode([
            'success' => true,
            'data' => [
                'filename' => $filename,
                'url' => BASE_URL . '/uploads/' . $filename,
                'size' => $_FILES['file']['size'],
                'type' => $_FILES['file']['type']
            ]
        ]);
        
        logActivity("File uploaded", 'INFO', [
            'filename' => $filename,
            'user_id' => $user['user_id']
        ]);
        
    } catch (Exception $e) {
        logError("Failed to upload file: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to process file upload']);
    }
}

// Batch upload handler
function handleBatchUpload($user) {
    if (!isset($_FILES['files']) || !isset($_POST['event_id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Files and event ID required']);
        return;
    }
    
    $eventId = intval($_POST['event_id']);
    $uploadedFiles = [];
    $errors = [];
    
    // Verify event ownership
    global $pdo;
    $stmt = $pdo->prepare("SELECT user_id FROM events WHERE id = ?");
    $stmt->execute([$eventId]);
    $event = $stmt->fetch();
    
    if (!$event || ($event['user_id'] != $user['user_id'] && $user['role'] !== 'admin')) {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        return;
    }
    
    $fileCount = count($_FILES['files']['name']);
    
    if ($fileCount > MAX_FILES_PER_EVENT) {
        http_response_code(400);
        echo json_encode(['error' => "Maximum $fileCount files allowed per event"]);
        return;
    }
    
    try {
        $pdo->beginTransaction();
        
        for ($i = 0; $i < $fileCount; $i++) {
            if ($_FILES['files']['error'][$i] === UPLOAD_ERR_OK) {
                $file = [
                    'name' => $_FILES['files']['name'][$i],
                    'type' => $_FILES['files']['type'][$i],
                    'tmp_name' => $_FILES['files']['tmp_name'][$i],
                    'error' => $_FILES['files']['error'][$i],
                    'size' => $_FILES['files']['size'][$i]
                ];
                
                $filename = uploadFile($file, UPLOAD_DIR . 'events/');
                
                if ($filename) {
                    // Save to database
                    $stmt = $pdo->prepare("
                        INSERT INTO event_images (event_id, image_path, image_type) 
                        VALUES (?, ?, 'main')
                    ");
                    $stmt->execute([$eventId, $filename]);
                    
                    $uploadedFiles[] = [
                        'filename' => $filename,
                        'url' => BASE_URL . '/uploads/events/' . $filename
                    ];
                } else {
                    $errors[] = "Failed to upload: " . $file['name'];
                }
            } else {
                $errors[] = "Upload error for: " . $_FILES['files']['name'][$i];
            }
        }
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'data' => [
                'uploaded_files' => $uploadedFiles,
                'errors' => $errors,
                'total_uploaded' => count($uploadedFiles)
            ]
        ]);
        
        logActivity("Batch upload completed", 'INFO', [
            'event_id' => $eventId,
            'files_uploaded' => count($uploadedFiles),
            'user_id' => $user['user_id']
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        logError("Failed to process batch upload: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to process batch upload']);
    }
}
?>
