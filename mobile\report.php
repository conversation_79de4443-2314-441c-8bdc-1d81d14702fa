<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require '../includes/db_connect.php';
require '../includes/auth_check.php';
require '../includes/functions.php';

global $EVENT_TYPES;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#2c3e50">
    <title>Report Event - DOWN-RWANDA</title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            padding-bottom: 80px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .form-container {
            background: white;
            border-radius: 20px;
            margin: 1rem;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .event-type-card {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .event-type-card:hover {
            border-color: var(--secondary-color);
            transform: translateY(-2px);
        }

        .event-type-card.selected {
            border-color: var(--primary-color);
            background: rgba(44, 62, 80, 0.1);
        }

        .event-type-card.emergency {
            border-color: var(--danger-color);
            background: rgba(231, 76, 60, 0.1);
        }

        .event-type-card.emergency.selected {
            border-color: var(--danger-color);
            background: rgba(231, 76, 60, 0.2);
        }

        .type-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .location-input {
            position: relative;
        }

        .location-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            border: none;
            background: var(--secondary-color);
            color: white;
            border-radius: 50%;
            width: 40px;
            height: 40px;
        }

        .image-upload-area {
            border: 2px dashed #ddd;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .image-upload-area:hover {
            border-color: var(--secondary-color);
            background: rgba(52, 152, 219, 0.1);
        }

        .image-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 1rem;
        }

        .image-preview img {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 10px;
            border: 2px solid #ddd;
        }

        .submit-btn {
            background: linear-gradient(135deg, var(--success-color), #229954);
            border: none;
            border-radius: 25px;
            padding: 1rem 2rem;
            color: white;
            font-weight: 600;
            width: 100%;
            margin-top: 1rem;
        }

        .emergency-btn {
            background: linear-gradient(135deg, var(--danger-color), #c0392b);
        }

        .loading {
            display: none;
        }

        .loading.show {
            display: block;
        }

        .form-step {
            display: none;
        }

        .form-step.active {
            display: block;
        }

        .progress-bar {
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <a href="index.php" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-arrow-left"></i> Back
                </a>
            </div>
            <h5 class="mb-0">Report Event</h5>
            <div>
                <button class="btn btn-outline-danger btn-sm" onclick="reportEmergency()">
                    <i class="fas fa-exclamation-triangle"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Form Container -->
    <div class="form-container">
        <!-- Progress Bar -->
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill" style="width: 25%"></div>
        </div>

        <form id="eventForm" enctype="multipart/form-data">
            <!-- Step 1: Event Type -->
            <div class="form-step active" id="step1">
                <h6 class="mb-3">What type of event are you reporting?</h6>

                <div class="row">
                    <?php foreach ($EVENT_TYPES as $type => $config): ?>
                    <div class="col-6 mb-3">
                        <div class="event-type-card <?= in_array($config['priority'], ['high', 'critical']) ? 'emergency' : '' ?>"
                             data-type="<?= $type ?>" onclick="selectEventType('<?= $type ?>')">
                            <i class="type-icon <?= $config['icon'] ?>" style="color: <?= $config['color'] ?>"></i>
                            <div class="fw-bold"><?= $config['name'] ?></div>
                            <small class="text-muted"><?= ucfirst($config['priority']) ?></small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <button type="button" class="btn btn-primary w-100 mt-3" onclick="nextStep()" disabled id="step1Next">
                    Continue <i class="fas fa-arrow-right ms-2"></i>
                </button>
            </div>

            <!-- Step 2: Location -->
            <div class="form-step" id="step2">
                <h6 class="mb-3">Where is this happening?</h6>

                <div class="mb-3">
                    <label class="form-label">Location</label>
                    <div class="location-input">
                        <input type="text" class="form-control" id="locationText" placeholder="Describe the location..." required>
                        <button type="button" class="location-btn" onclick="getCurrentLocation()" title="Use current location">
                            <i class="fas fa-location-arrow"></i>
                        </button>
                    </div>
                    <small class="text-muted">Tap the location button to use your current position</small>
                </div>

                <input type="hidden" id="latitude" name="latitude">
                <input type="hidden" id="longitude" name="longitude">

                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-secondary flex-fill" onclick="prevStep()">
                        <i class="fas fa-arrow-left me-2"></i> Back
                    </button>
                    <button type="button" class="btn btn-primary flex-fill" onclick="nextStep()" disabled id="step2Next">
                        Continue <i class="fas fa-arrow-right ms-2"></i>
                    </button>
                </div>
            </div>

            <!-- Step 3: Details -->
            <div class="form-step" id="step3">
                <h6 class="mb-3">Tell us more about what happened</h6>

                <div class="mb-3">
                    <label class="form-label">Title</label>
                    <input type="text" class="form-control" id="eventTitle" name="title" placeholder="Brief description..." required>
                </div>

                <div class="mb-3">
                    <label class="form-label">Description</label>
                    <textarea class="form-control" id="eventDescription" name="description" rows="4"
                              placeholder="Provide more details about what happened..."></textarea>
                </div>

                <div class="mb-3">
                    <label class="form-label">Severity Level</label>
                    <select class="form-select" id="severityLevel" name="severity_level" required>
                        <option value="low">Low - Minor incident</option>
                        <option value="medium" selected>Medium - Moderate incident</option>
                        <option value="high">High - Serious incident</option>
                        <option value="critical">Critical - Emergency situation</option>
                    </select>
                </div>

                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-secondary flex-fill" onclick="prevStep()">
                        <i class="fas fa-arrow-left me-2"></i> Back
                    </button>
                    <button type="button" class="btn btn-primary flex-fill" onclick="nextStep()">
                        Continue <i class="fas fa-arrow-right ms-2"></i>
                    </button>
                </div>
            </div>

            <!-- Step 4: Images -->
            <div class="form-step" id="step4">
                <h6 class="mb-3">Add photos (optional)</h6>

                <div class="image-upload-area" onclick="document.getElementById('imageInput').click()">
                    <i class="fas fa-camera fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">Tap to add photos</p>
                    <small class="text-muted">You can add up to 5 images</small>
                </div>

                <input type="file" id="imageInput" name="images[]" multiple accept="image/*" style="display: none;" onchange="previewImages(this)">

                <div class="image-preview" id="imagePreview"></div>

                <div class="d-flex gap-2 mt-3">
                    <button type="button" class="btn btn-outline-secondary flex-fill" onclick="prevStep()">
                        <i class="fas fa-arrow-left me-2"></i> Back
                    </button>
                    <button type="submit" class="submit-btn flex-fill" id="submitBtn">
                        <span class="submit-text">Submit Report</span>
                        <span class="loading">
                            <i class="fas fa-spinner fa-spin"></i> Submitting...
                        </span>
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentStep = 1;
        let selectedEventType = null;
        let userLocation = null;

        function selectEventType(type) {
            selectedEventType = type;

            // Update UI
            document.querySelectorAll('.event-type-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-type="${type}"]`).classList.add('selected');

            // Enable next button
            document.getElementById('step1Next').disabled = false;

            // Auto-advance for emergency types
            const emergencyTypes = ['accident', 'fire', 'medical', 'crime'];
            if (emergencyTypes.includes(type)) {
                setTimeout(() => nextStep(), 500);
            }
        }

        function nextStep() {
            if (currentStep < 4) {
                // Validate current step
                if (!validateStep(currentStep)) return;

                // Hide current step
                document.getElementById(`step${currentStep}`).classList.remove('active');

                // Show next step
                currentStep++;
                document.getElementById(`step${currentStep}`).classList.add('active');

                // Update progress
                updateProgress();

                // Auto-focus on next step
                focusNextStep();
            }
        }

        function prevStep() {
            if (currentStep > 1) {
                document.getElementById(`step${currentStep}`).classList.remove('active');
                currentStep--;
                document.getElementById(`step${currentStep}`).classList.add('active');
                updateProgress();
            }
        }

        function updateProgress() {
            const progress = (currentStep / 4) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        function validateStep(step) {
            switch (step) {
                case 1:
                    return selectedEventType !== null;
                case 2:
                    return userLocation !== null || document.getElementById('locationText').value.trim() !== '';
                case 3:
                    return document.getElementById('eventTitle').value.trim() !== '';
                default:
                    return true;
            }
        }

        function focusNextStep() {
            setTimeout(() => {
                switch (currentStep) {
                    case 2:
                        document.getElementById('locationText').focus();
                        break;
                    case 3:
                        document.getElementById('eventTitle').focus();
                        break;
                }
            }, 300);
        }

        function getCurrentLocation() {
            const btn = document.querySelector('.location-btn');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            if ('geolocation' in navigator) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        userLocation = {
                            lat: position.coords.latitude,
                            lng: position.coords.longitude
                        };

                        document.getElementById('latitude').value = userLocation.lat;
                        document.getElementById('longitude').value = userLocation.lng;
                        document.getElementById('locationText').value = `Current location (${userLocation.lat.toFixed(4)}, ${userLocation.lng.toFixed(4)})`;

                        btn.innerHTML = '<i class="fas fa-check"></i>';
                        btn.style.background = '#27ae60';

                        // Enable next button
                        document.getElementById('step2Next').disabled = false;

                        setTimeout(() => {
                            btn.innerHTML = '<i class="fas fa-location-arrow"></i>';
                            btn.style.background = '#3498db';
                        }, 2000);
                    },
                    (error) => {
                        console.error('Geolocation error:', error);
                        btn.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                        btn.style.background = '#e74c3c';

                        setTimeout(() => {
                            btn.innerHTML = '<i class="fas fa-location-arrow"></i>';
                            btn.style.background = '#3498db';
                        }, 2000);

                        alert('Unable to get your location. Please enter it manually.');
                    }
                );
            } else {
                alert('Geolocation is not supported by this browser.');
                btn.innerHTML = '<i class="fas fa-location-arrow"></i>';
            }
        }

        // Enable step 2 next button when location is entered
        document.getElementById('locationText').addEventListener('input', function() {
            document.getElementById('step2Next').disabled = this.value.trim() === '';
        });

        function previewImages(input) {
            const preview = document.getElementById('imagePreview');
            preview.innerHTML = '';

            if (input.files) {
                Array.from(input.files).slice(0, 5).forEach((file, index) => {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        preview.appendChild(img);
                    };
                    reader.readAsDataURL(file);
                });
            }
        }

        // Form submission
        document.getElementById('eventForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const submitBtn = document.getElementById('submitBtn');
            const submitText = submitBtn.querySelector('.submit-text');
            const loading = submitBtn.querySelector('.loading');

            // Show loading state
            submitText.style.display = 'none';
            loading.style.display = 'inline';
            submitBtn.disabled = true;

            try {
                const formData = new FormData();
                formData.append('type', selectedEventType);
                formData.append('title', document.getElementById('eventTitle').value);
                formData.append('description', document.getElementById('eventDescription').value);
                formData.append('severity_level', document.getElementById('severityLevel').value);
                formData.append('latitude', document.getElementById('latitude').value || 0);
                formData.append('longitude', document.getElementById('longitude').value || 0);

                // Add images
                const imageInput = document.getElementById('imageInput');
                if (imageInput.files.length > 0) {
                    Array.from(imageInput.files).forEach((file, index) => {
                        formData.append(`images[]`, file);
                    });
                }

                const response = await fetch('../api/v2/events', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`
                    },
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // Show success message
                    showSuccessMessage();

                    // Reset form after delay
                    setTimeout(() => {
                        window.location.href = 'index.php';
                    }, 2000);
                } else {
                    throw new Error(result.error || 'Failed to submit report');
                }

            } catch (error) {
                console.error('Submission error:', error);
                alert('Failed to submit report: ' + error.message);

                // Reset button state
                submitText.style.display = 'inline';
                loading.style.display = 'none';
                submitBtn.disabled = false;
            }
        });

        function showSuccessMessage() {
            const container = document.querySelector('.form-container');
            container.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                    <h4>Report Submitted Successfully!</h4>
                    <p class="text-muted">Thank you for your report. We'll review it shortly.</p>
                    <div class="spinner-border text-primary mt-3" role="status">
                        <span class="visually-hidden">Redirecting...</span>
                    </div>
                </div>
            `;
        }

        function reportEmergency() {
            if (window.app && window.app.reportEmergency) {
                window.app.reportEmergency();
            } else {
                // Fallback emergency reporting
                window.location.href = 'index.php?emergency=true';
            }
        }

        // Auto-get location on page load
        window.addEventListener('load', function() {
            if ('geolocation' in navigator) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        userLocation = {
                            lat: position.coords.latitude,
                            lng: position.coords.longitude
                        };
                        document.getElementById('latitude').value = userLocation.lat;
                        document.getElementById('longitude').value = userLocation.lng;
                    },
                    (error) => {
                        console.log('Background geolocation failed:', error);
                    }
                );
            }
        });
    </script>
</body>
</html>
