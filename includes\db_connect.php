<?php
$host = 'localhost';
$dbname = 'down_rwanda';
$username = 'root';
$password = '';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    // Enable MySQL spatial functions
    $pdo->exec("SET sql_mode = ''");
    $pdo->exec("SET GLOBAL sql_mode=(SELECT REPLACE(@@sql_mode,'ONLY_FULL_GROUP_BY',''))");
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Create tables if they don't exist
$pdo->exec("
    CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('user','organization','police','admin') DEFAULT 'user',
        phone VARCHAR(20),
        last_location POINT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
");

$pdo->exec("
    CREATE TABLE IF NOT EXISTS events (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(100) NOT NULL,
        type ENUM('accident','show','party','other') NOT NULL,
        description TEXT,
        latitude DECIMAL(10, 8) NOT NULL,
        longitude DECIMAL(11, 8) NOT NULL,
        is_paid BOOLEAN DEFAULT FALSE,
        payment_proof VARCHAR(255),
        is_approved BOOLEAN DEFAULT FALSE,
        user_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    )
");

$pdo->exec("
    CREATE TABLE IF NOT EXISTS external_events (
        id INT AUTO_INCREMENT PRIMARY KEY,
        source VARCHAR(50) NOT NULL,
        external_id VARCHAR(100) NOT NULL,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        latitude DECIMAL(10, 8),
        longitude DECIMAL(11, 8),
        event_date DATETIME,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_external (source, external_id)
    )
");

$pdo->exec("
    CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        message TEXT NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        type ENUM('info','warning','danger','success') DEFAULT 'info',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )
");

$pdo->exec("
    CREATE TABLE IF NOT EXISTS archived_events (
        id INT AUTO_INCREMENT PRIMARY KEY,
        original_id INT,
        title VARCHAR(100) NOT NULL,
        type ENUM('accident','show','party','other') NOT NULL,
        description TEXT,
        latitude DECIMAL(10, 8) NOT NULL,
        longitude DECIMAL(11, 8) NOT NULL,
        is_paid BOOLEAN DEFAULT FALSE,
        payment_proof VARCHAR(255),
        is_approved BOOLEAN DEFAULT FALSE,
        user_id INT NOT NULL,
        created_at TIMESTAMP,
        archived_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
");

$pdo->exec("
    CREATE TABLE IF NOT EXISTS event_analytics (
        id INT AUTO_INCREMENT PRIMARY KEY,
        event_id INT,
        views INT DEFAULT 0,
        shares INT DEFAULT 0,
        response_time INT DEFAULT 0,
        severity_score DECIMAL(3,2) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
    )
");

$pdo->exec("
    CREATE TABLE IF NOT EXISTS police_units (
        id INT AUTO_INCREMENT PRIMARY KEY,
        unit_name VARCHAR(100) NOT NULL,
        officer_id INT,
        current_location POINT,
        status ENUM('available','busy','offline') DEFAULT 'available',
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (officer_id) REFERENCES users(id)
    )
");

$pdo->exec("
    CREATE TABLE IF NOT EXISTS emergency_responses (
        id INT AUTO_INCREMENT PRIMARY KEY,
        event_id INT NOT NULL,
        police_unit_id INT,
        response_time INT,
        status ENUM('dispatched','arrived','completed') DEFAULT 'dispatched',
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (event_id) REFERENCES events(id),
        FOREIGN KEY (police_unit_id) REFERENCES police_units(id)
    )
");

$pdo->exec("
    CREATE TABLE IF NOT EXISTS user_sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        session_token VARCHAR(255) NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )
");

$pdo->exec("
    CREATE TABLE IF NOT EXISTS api_keys (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        api_key VARCHAR(255) NOT NULL UNIQUE,
        name VARCHAR(100) NOT NULL,
        permissions JSON,
        is_active BOOLEAN DEFAULT TRUE,
        last_used TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )
");

$pdo->exec("
    CREATE TABLE IF NOT EXISTS event_images (
        id INT AUTO_INCREMENT PRIMARY KEY,
        event_id INT NOT NULL,
        image_path VARCHAR(255) NOT NULL,
        image_type ENUM('main','evidence','damage') DEFAULT 'main',
        ai_analysis JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
    )
");

// Add missing columns to existing tables
try {
    $pdo->exec("ALTER TABLE events ADD COLUMN description TEXT AFTER type");
} catch (PDOException $e) {
    // Column already exists
}

try {
    $pdo->exec("ALTER TABLE events ADD COLUMN image_path VARCHAR(255) AFTER payment_proof");
} catch (PDOException $e) {
    // Column already exists
}

try {
    $pdo->exec("ALTER TABLE events ADD COLUMN severity_level ENUM('low','medium','high','critical') DEFAULT 'medium' AFTER type");
} catch (PDOException $e) {
    // Column already exists
}

try {
    $pdo->exec("ALTER TABLE users ADD COLUMN email VARCHAR(100) AFTER phone");
} catch (PDOException $e) {
    // Column already exists
}

try {
    $pdo->exec("ALTER TABLE users ADD COLUMN avatar VARCHAR(255) AFTER email");
} catch (PDOException $e) {
    // Column already exists
}

try {
    $pdo->exec("ALTER TABLE users ADD COLUMN is_verified BOOLEAN DEFAULT FALSE AFTER avatar");
} catch (PDOException $e) {
    // Column already exists
}
?>