<?php
function sendSMS($phone, $message) {
    // Using Twilio-like API
    $account_sid = 'YOUR_ACCOUNT_SID';
    $auth_token = 'YOUR_AUTH_TOKEN';
    
    $url = "https://api.twilio.com/2010-04-01/Accounts/$account_sid/Messages.json";
    
    $data = [
        'To' => '+250'.$phone, // Rwanda country code
        'From' => 'DOWN-RWANDA',
        'Body' => $message
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_USERPWD, "$account_sid:$auth_token");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    file_put_contents('../logs/sms.log', date('Y-m-d H:i:s')." SMS to $phone\n", FILE_APPEND);
    return $response;
}
?>