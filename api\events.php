<?php
header("Content-Type: application/json");
require '../includes/db_connect.php';
require '../includes/functions.php';

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        $type = isset($_GET['type']) ? sanitizeInput($_GET['type']) : null;
        $lat = isset($_GET['lat']) ? floatval($_GET['lat']) : null;
        $lng = isset($_GET['lng']) ? floatval($_GET['lng']) : null;
        $radius = isset($_GET['radius']) ? intval($_GET['radius']) : 10; // km

        $query = "SELECT * FROM events WHERE is_approved = TRUE";
        $params = [];

        if ($type) {
            $query .= " AND type = ?";
            $params[] = $type;
        }

        if ($lat && $lng) {
            $query .= " HAVING ST_Distance_Sphere(POINT(?, ?), POINT(longitude, latitude)) <= ?";
            array_push($params, $lng, $lat, $radius * 1000);
        }

        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        echo json_encode($stmt->fetchAll());
        break;

    case 'POST':
        session_start();
        require '../includes/auth_check.php';

        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate and process event creation
        // ... (similar to add_event.php but returns JSON)
        
        break;

    default:
        http_response_code(405);
        echo json_encode(["error" => "Method not allowed"]);
}
?>