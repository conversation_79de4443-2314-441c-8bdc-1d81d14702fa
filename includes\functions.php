<?php

function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

function validateCoordinates($lat, $lng) {
    return is_numeric($lat) && is_numeric($lng) && 
           $lat >= -90 && $lat <= 90 && 
           $lng >= -180 && $lng <= 180;
}

function logActivity($message) {
    $logEntry = "[" . date('Y-m-d H:i:s') . "] " . $message . PHP_EOL;
    file_put_contents(LOG_DIR . 'activity.log', $logEntry, FILE_APPEND);
}

function sendNotification($userId, $message) {
    global $pdo;
    $stmt = $pdo->prepare("INSERT INTO notifications (user_id, message) VALUES (?, ?)");
    $stmt->execute([$userId, $message]);
    logActivity("Notification sent to user $userId: $message");
}

function getDistance($lat1, $lon1, $lat2, $lon2) {
    $theta = $lon1 - $lon2;
    $dist = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) + 
            cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
    return rad2deg(acos($dist)) * 60 * 1.85316; // in kilometers
}
?>