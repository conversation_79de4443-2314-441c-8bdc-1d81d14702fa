<?php
require_once 'config.php';

// ============================================================================
// SECURITY & VALIDATION FUNCTIONS
// ============================================================================

function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

function validatePhone($phone) {
    // Rwanda phone number validation
    return preg_match('/^(\+250|250|0)?[7][0-9]{8}$/', $phone);
}

function validateCoordinates($lat, $lng) {
    return is_numeric($lat) && is_numeric($lng) &&
           $lat >= -90 && $lat <= 90 &&
           $lng >= -180 && $lng <= 180;
}

function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}

function hashPassword($password) {
    return password_hash($password, PASSWORD_ARGON2ID, [
        'memory_cost' => 65536,
        'time_cost' => 4,
        'threads' => 3
    ]);
}

function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

function generateApiKey() {
    return 'dr_' . bin2hex(random_bytes(20));
}

// ============================================================================
// JWT TOKEN FUNCTIONS
// ============================================================================

function generateJWT($payload) {
    $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
    $payload = json_encode($payload);

    $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
    $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

    $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, JWT_SECRET, true);
    $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

    return $base64Header . "." . $base64Payload . "." . $base64Signature;
}

function verifyJWT($token) {
    $parts = explode('.', $token);
    if (count($parts) !== 3) {
        return false;
    }

    $header = base64_decode(str_replace(['-', '_'], ['+', '/'], $parts[0]));
    $payload = base64_decode(str_replace(['-', '_'], ['+', '/'], $parts[1]));
    $signature = str_replace(['-', '_'], ['+', '/'], $parts[2]);

    $expectedSignature = hash_hmac('sha256', $parts[0] . "." . $parts[1], JWT_SECRET, true);
    $expectedSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($expectedSignature));

    if (!hash_equals($signature, $expectedSignature)) {
        return false;
    }

    $payloadData = json_decode($payload, true);
    if ($payloadData['exp'] < time()) {
        return false;
    }

    return $payloadData;
}

// ============================================================================
// LOGGING & MONITORING FUNCTIONS
// ============================================================================

function logActivity($message, $level = 'INFO', $context = []) {
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? ' | Context: ' . json_encode($context) : '';
    $logEntry = "[$timestamp] [$level] $message$contextStr" . PHP_EOL;

    $logFile = LOG_DIR . 'activity.log';
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);

    // Rotate log files if they get too large (10MB)
    if (file_exists($logFile) && filesize($logFile) > 10 * 1024 * 1024) {
        rename($logFile, LOG_DIR . 'activity_' . date('Y-m-d_H-i-s') . '.log');
    }
}

function logError($message, $context = []) {
    logActivity($message, 'ERROR', $context);
}

function logWarning($message, $context = []) {
    logActivity($message, 'WARNING', $context);
}

function logDebug($message, $context = []) {
    if (LOG_LEVEL === 'DEBUG') {
        logActivity($message, 'DEBUG', $context);
    }
}

// ============================================================================
// NOTIFICATION FUNCTIONS
// ============================================================================

function sendNotification($userId, $message, $type = 'info') {
    global $pdo;
    try {
        $stmt = $pdo->prepare("INSERT INTO notifications (user_id, message, type) VALUES (?, ?, ?)");
        $stmt->execute([$userId, $message, $type]);
        logActivity("Notification sent to user $userId: $message");
        return true;
    } catch (PDOException $e) {
        logError("Failed to send notification: " . $e->getMessage());
        return false;
    }
}

function sendBulkNotification($userIds, $message, $type = 'info') {
    global $pdo;
    try {
        $stmt = $pdo->prepare("INSERT INTO notifications (user_id, message, type) VALUES (?, ?, ?)");
        foreach ($userIds as $userId) {
            $stmt->execute([$userId, $message, $type]);
        }
        logActivity("Bulk notification sent to " . count($userIds) . " users");
        return true;
    } catch (PDOException $e) {
        logError("Failed to send bulk notification: " . $e->getMessage());
        return false;
    }
}

function markNotificationAsRead($notificationId, $userId) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("UPDATE notifications SET is_read = TRUE WHERE id = ? AND user_id = ?");
        $stmt->execute([$notificationId, $userId]);
        return true;
    } catch (PDOException $e) {
        logError("Failed to mark notification as read: " . $e->getMessage());
        return false;
    }
}

// ============================================================================
// GEOLOCATION & DISTANCE FUNCTIONS
// ============================================================================

function getDistance($lat1, $lon1, $lat2, $lon2) {
    $earthRadius = 6371; // Earth's radius in kilometers

    $dLat = deg2rad($lat2 - $lat1);
    $dLon = deg2rad($lon2 - $lon1);

    $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
    $c = 2 * atan2(sqrt($a), sqrt(1-$a));

    return $earthRadius * $c;
}

function findNearbyUsers($latitude, $longitude, $radiusKm = 10, $userRole = null) {
    global $pdo;

    $query = "
        SELECT u.id, u.username, u.role, u.phone, u.email,
               ST_Distance_Sphere(POINT(?, ?), u.last_location) as distance
        FROM users u
        WHERE u.last_location IS NOT NULL
        AND ST_Distance_Sphere(POINT(?, ?), u.last_location) <= ?
    ";

    $params = [$longitude, $latitude, $longitude, $latitude, $radiusKm * 1000];

    if ($userRole) {
        $query .= " AND u.role = ?";
        $params[] = $userRole;
    }

    $query .= " ORDER BY distance ASC";

    try {
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        logError("Failed to find nearby users: " . $e->getMessage());
        return [];
    }
}

function updateUserLocation($userId, $latitude, $longitude) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("UPDATE users SET last_location = POINT(?, ?) WHERE id = ?");
        $stmt->execute([$longitude, $latitude, $userId]);
        return true;
    } catch (PDOException $e) {
        logError("Failed to update user location: " . $e->getMessage());
        return false;
    }
}

// ============================================================================
// FILE HANDLING FUNCTIONS
// ============================================================================

function uploadFile($file, $destination, $allowedTypes = null) {
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        return false;
    }

    $allowedTypes = $allowedTypes ?: ALLOWED_FILE_TYPES;

    if (!in_array($file['type'], $allowedTypes)) {
        logWarning("Invalid file type attempted: " . $file['type']);
        return false;
    }

    if ($file['size'] > MAX_FILE_SIZE) {
        logWarning("File size too large: " . $file['size']);
        return false;
    }

    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '.' . $extension;
    $filepath = $destination . $filename;

    if (!file_exists(dirname($filepath))) {
        mkdir(dirname($filepath), 0755, true);
    }

    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        logActivity("File uploaded successfully: $filepath");
        return $filename;
    }

    logError("Failed to upload file: " . $file['name']);
    return false;
}

function deleteFile($filepath) {
    if (file_exists($filepath)) {
        if (unlink($filepath)) {
            logActivity("File deleted: $filepath");
            return true;
        }
    }
    return false;
}

function generateThumbnail($imagePath, $width = 200, $height = 200) {
    if (!extension_loaded('gd')) {
        return false;
    }

    $imageInfo = getimagesize($imagePath);
    if (!$imageInfo) {
        return false;
    }

    $originalWidth = $imageInfo[0];
    $originalHeight = $imageInfo[1];
    $imageType = $imageInfo[2];

    // Create image resource based on type
    switch ($imageType) {
        case IMAGETYPE_JPEG:
            $sourceImage = imagecreatefromjpeg($imagePath);
            break;
        case IMAGETYPE_PNG:
            $sourceImage = imagecreatefrompng($imagePath);
            break;
        default:
            return false;
    }

    // Calculate new dimensions
    $ratio = min($width / $originalWidth, $height / $originalHeight);
    $newWidth = $originalWidth * $ratio;
    $newHeight = $originalHeight * $ratio;

    // Create thumbnail
    $thumbnail = imagecreatetruecolor($newWidth, $newHeight);
    imagecopyresampled($thumbnail, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);

    // Save thumbnail
    $thumbnailPath = dirname($imagePath) . '/thumb_' . basename($imagePath);

    switch ($imageType) {
        case IMAGETYPE_JPEG:
            imagejpeg($thumbnail, $thumbnailPath, 85);
            break;
        case IMAGETYPE_PNG:
            imagepng($thumbnail, $thumbnailPath);
            break;
    }

    imagedestroy($sourceImage);
    imagedestroy($thumbnail);

    return basename($thumbnailPath);
}

// ============================================================================
// CACHING FUNCTIONS
// ============================================================================

function getCacheKey($prefix, $params = []) {
    return $prefix . '_' . md5(serialize($params));
}

function getCache($key) {
    if (!ENABLE_CACHING) {
        return false;
    }

    $cacheFile = CACHE_DIR . $key . '.cache';

    if (!file_exists($cacheFile)) {
        return false;
    }

    $cacheData = unserialize(file_get_contents($cacheFile));

    if ($cacheData['expires'] < time()) {
        unlink($cacheFile);
        return false;
    }

    return $cacheData['data'];
}

function setCache($key, $data, $duration = null) {
    if (!ENABLE_CACHING) {
        return false;
    }

    $duration = $duration ?: CACHE_DURATION;
    $cacheData = [
        'data' => $data,
        'expires' => time() + $duration
    ];

    $cacheFile = CACHE_DIR . $key . '.cache';
    return file_put_contents($cacheFile, serialize($cacheData)) !== false;
}

function clearCache($pattern = '*') {
    $files = glob(CACHE_DIR . $pattern . '.cache');
    foreach ($files as $file) {
        unlink($file);
    }
    return count($files);
}

// ============================================================================
// ANALYTICS & REPORTING FUNCTIONS
// ============================================================================

function trackEventView($eventId, $userId = null) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            INSERT INTO event_analytics (event_id, views)
            VALUES (?, 1)
            ON DUPLICATE KEY UPDATE views = views + 1
        ");
        $stmt->execute([$eventId]);

        if ($userId) {
            logActivity("Event $eventId viewed by user $userId");
        }

        return true;
    } catch (PDOException $e) {
        logError("Failed to track event view: " . $e->getMessage());
        return false;
    }
}

function getEventAnalytics($eventId) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("SELECT * FROM event_analytics WHERE event_id = ?");
        $stmt->execute([$eventId]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        logError("Failed to get event analytics: " . $e->getMessage());
        return false;
    }
}

function getSystemStats() {
    global $pdo;

    $cacheKey = getCacheKey('system_stats');
    $stats = getCache($cacheKey);

    if ($stats === false) {
        try {
            $stats = $pdo->query("
                SELECT
                    (SELECT COUNT(*) FROM users) AS total_users,
                    (SELECT COUNT(*) FROM users WHERE role = 'police') AS police_users,
                    (SELECT COUNT(*) FROM users WHERE role = 'organization') AS org_users,
                    (SELECT COUNT(*) FROM events) AS total_events,
                    (SELECT COUNT(*) FROM events WHERE type = 'accident') AS accidents,
                    (SELECT COUNT(*) FROM events WHERE is_approved = TRUE) AS approved_events,
                    (SELECT COUNT(*) FROM events WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) AS events_24h,
                    (SELECT COUNT(*) FROM notifications WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) AS notifications_24h
            ")->fetch();

            setCache($cacheKey, $stats, 300); // Cache for 5 minutes
        } catch (PDOException $e) {
            logError("Failed to get system stats: " . $e->getMessage());
            return false;
        }
    }

    return $stats;
}

// ============================================================================
// AI/ML HELPER FUNCTIONS
// ============================================================================

function analyzeImageSeverity($imagePath) {
    if (!AI_IMAGE_ANALYSIS) {
        return ['severity' => 'medium', 'confidence' => 0];
    }

    // Placeholder for AI image analysis
    // In production, this would call an AI service like Google Vision API
    $analysis = [
        'severity' => 'medium',
        'confidence' => 0.75,
        'detected_objects' => ['vehicle', 'road'],
        'damage_assessment' => 'moderate'
    ];

    logActivity("Image analysis completed for: $imagePath", 'DEBUG', $analysis);
    return $analysis;
}

function predictResponseTime($latitude, $longitude, $eventType) {
    if (!AI_RESPONSE_PREDICTION) {
        return 15; // Default 15 minutes
    }

    // Placeholder for ML prediction
    // This would use historical data to predict response times
    $baseTime = 10;
    $trafficFactor = 1.2;
    $typeFactor = ($eventType === 'accident') ? 1.5 : 1.0;

    return round($baseTime * $trafficFactor * $typeFactor);
}

function categorizeEventAutomatically($title, $description, $imagePath = null) {
    $keywords = [
        'accident' => ['accident', 'crash', 'collision', 'injured', 'ambulance'],
        'fire' => ['fire', 'smoke', 'burning', 'flames'],
        'crime' => ['theft', 'robbery', 'assault', 'crime', 'stolen'],
        'medical' => ['medical', 'emergency', 'heart', 'unconscious']
    ];

    $text = strtolower($title . ' ' . $description);
    $scores = [];

    foreach ($keywords as $category => $words) {
        $score = 0;
        foreach ($words as $word) {
            if (strpos($text, $word) !== false) {
                $score++;
            }
        }
        $scores[$category] = $score;
    }

    $bestCategory = array_keys($scores, max($scores))[0];
    return max($scores) > 0 ? $bestCategory : 'other';
}

// ============================================================================
// EMERGENCY RESPONSE FUNCTIONS
// ============================================================================

function dispatchEmergencyServices($eventId, $latitude, $longitude, $eventType) {
    global $pdo, $EVENT_TYPES;

    if (!isset($EVENT_TYPES[$eventType])) {
        return false;
    }

    $requiredServices = $EVENT_TYPES[$eventType]['emergency_services'];
    $dispatched = [];

    foreach ($requiredServices as $serviceType) {
        $units = findNearbyPoliceUnits($latitude, $longitude, $serviceType);

        foreach ($units as $unit) {
            if ($unit['status'] === 'available') {
                // Dispatch the unit
                $stmt = $pdo->prepare("
                    INSERT INTO emergency_responses (event_id, police_unit_id, status)
                    VALUES (?, ?, 'dispatched')
                ");
                $stmt->execute([$eventId, $unit['id']]);

                // Update unit status
                $pdo->prepare("UPDATE police_units SET status = 'busy' WHERE id = ?")
                    ->execute([$unit['id']]);

                $dispatched[] = $unit;

                // Send notification to unit
                if ($unit['officer_id']) {
                    sendNotification(
                        $unit['officer_id'],
                        "Emergency dispatch: $eventType at $latitude, $longitude",
                        'danger'
                    );
                }

                break; // Only dispatch one unit per service type
            }
        }
    }

    logActivity("Emergency services dispatched for event $eventId", 'INFO', [
        'event_type' => $eventType,
        'location' => "$latitude, $longitude",
        'dispatched_units' => count($dispatched)
    ]);

    return $dispatched;
}

function findNearbyPoliceUnits($latitude, $longitude, $unitType = 'police', $radiusKm = 20) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT pu.*, u.username, u.phone,
                   ST_Distance_Sphere(POINT(?, ?), pu.current_location) as distance
            FROM police_units pu
            JOIN users u ON pu.officer_id = u.id
            WHERE pu.current_location IS NOT NULL
            AND ST_Distance_Sphere(POINT(?, ?), pu.current_location) <= ?
            AND u.role = ?
            ORDER BY distance ASC, pu.status ASC
        ");

        $stmt->execute([
            $longitude, $latitude,
            $longitude, $latitude,
            $radiusKm * 1000,
            $unitType
        ]);

        return $stmt->fetchAll();
    } catch (PDOException $e) {
        logError("Failed to find nearby police units: " . $e->getMessage());
        return [];
    }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

function formatTimeAgo($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';

    return date('M j, Y', strtotime($datetime));
}

function generateQRCode($data, $size = 200) {
    // Placeholder for QR code generation
    // In production, use a library like endroid/qr-code
    $qrUrl = "https://api.qrserver.com/v1/create-qr-code/?size={$size}x{$size}&data=" . urlencode($data);
    return $qrUrl;
}

function sendWebhook($url, $data) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    logActivity("Webhook sent to $url", 'DEBUG', ['response_code' => $httpCode]);

    return $httpCode >= 200 && $httpCode < 300;
}

function translateText($text, $targetLanguage = 'en') {
    if (!TRANSLATION_API_KEY) {
        return $text;
    }

    // Placeholder for translation service
    // In production, use Google Translate API or similar
    return $text;
}

function compressImage($imagePath, $quality = 85) {
    if (!extension_loaded('gd')) {
        return false;
    }

    $imageInfo = getimagesize($imagePath);
    if (!$imageInfo) {
        return false;
    }

    switch ($imageInfo[2]) {
        case IMAGETYPE_JPEG:
            $image = imagecreatefromjpeg($imagePath);
            imagejpeg($image, $imagePath, $quality);
            break;
        case IMAGETYPE_PNG:
            $image = imagecreatefrompng($imagePath);
            imagepng($image, $imagePath, round($quality / 10));
            break;
        default:
            return false;
    }

    imagedestroy($image);
    return true;
}