<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

require_once '../../includes/db_connect.php';
require_once '../../includes/functions.php';
require_once '../../config.php';

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// API Rate Limiting
$clientIp = $_SERVER['REMOTE_ADDR'];
$rateLimitKey = "rate_limit_$clientIp";
$requests = getCache($rateLimitKey) ?: 0;

if ($requests >= API_RATE_LIMIT) {
    http_response_code(429);
    echo json_encode(['error' => 'Rate limit exceeded']);
    exit;
}

setCache($rateLimitKey, $requests + 1, 3600); // 1 hour window

// Authentication middleware
function authenticateRequest() {
    $headers = getallheaders();
    $token = null;
    
    if (isset($headers['Authorization'])) {
        $authHeader = $headers['Authorization'];
        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            $token = $matches[1];
        }
    } elseif (isset($headers['X-API-Key'])) {
        return authenticateApiKey($headers['X-API-Key']);
    }
    
    if (!$token) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        exit;
    }
    
    $payload = verifyJWT($token);
    if (!$payload) {
        http_response_code(401);
        echo json_encode(['error' => 'Invalid or expired token']);
        exit;
    }
    
    return $payload;
}

function authenticateApiKey($apiKey) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT ak.*, u.id as user_id, u.username, u.role 
            FROM api_keys ak 
            JOIN users u ON ak.user_id = u.id 
            WHERE ak.api_key = ? AND ak.is_active = TRUE
        ");
        $stmt->execute([$apiKey]);
        $keyData = $stmt->fetch();
        
        if (!$keyData) {
            http_response_code(401);
            echo json_encode(['error' => 'Invalid API key']);
            exit;
        }
        
        // Update last used timestamp
        $pdo->prepare("UPDATE api_keys SET last_used = NOW() WHERE id = ?")
            ->execute([$keyData['id']]);
        
        return [
            'user_id' => $keyData['user_id'],
            'username' => $keyData['username'],
            'role' => $keyData['role'],
            'permissions' => json_decode($keyData['permissions'], true)
        ];
    } catch (PDOException $e) {
        logError("API key authentication failed: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Authentication error']);
        exit;
    }
}

// Router
$requestUri = $_SERVER['REQUEST_URI'];
$requestMethod = $_SERVER['REQUEST_METHOD'];
$path = parse_url($requestUri, PHP_URL_PATH);
$pathParts = explode('/', trim($path, '/'));

// Remove 'api/v2' from path parts
array_shift($pathParts); // 'api'
array_shift($pathParts); // 'v2'

$endpoint = $pathParts[0] ?? '';
$id = $pathParts[1] ?? null;

try {
    switch ($endpoint) {
        case 'auth':
            handleAuth($requestMethod);
            break;
            
        case 'events':
            $user = authenticateRequest();
            handleEvents($requestMethod, $id, $user);
            break;
            
        case 'users':
            $user = authenticateRequest();
            handleUsers($requestMethod, $id, $user);
            break;
            
        case 'notifications':
            $user = authenticateRequest();
            handleNotifications($requestMethod, $id, $user);
            break;
            
        case 'analytics':
            $user = authenticateRequest();
            handleAnalytics($requestMethod, $user);
            break;
            
        case 'emergency':
            $user = authenticateRequest();
            handleEmergency($requestMethod, $user);
            break;
            
        case 'upload':
            $user = authenticateRequest();
            handleUpload($requestMethod, $user);
            break;
            
        case 'health':
            handleHealthCheck();
            break;
            
        default:
            http_response_code(404);
            echo json_encode(['error' => 'Endpoint not found']);
    }
} catch (Exception $e) {
    logError("API Error: " . $e->getMessage(), ['endpoint' => $endpoint, 'method' => $requestMethod]);
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}

// ============================================================================
// AUTHENTICATION HANDLERS
// ============================================================================

function handleAuth($method) {
    global $pdo;
    
    if ($method !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'login':
            $username = sanitizeInput($input['username'] ?? '');
            $password = $input['password'] ?? '';
            
            if (!$username || !$password) {
                http_response_code(400);
                echo json_encode(['error' => 'Username and password required']);
                return;
            }
            
            try {
                $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
                $stmt->execute([$username]);
                $user = $stmt->fetch();
                
                if (!$user || !verifyPassword($password, $user['password'])) {
                    http_response_code(401);
                    echo json_encode(['error' => 'Invalid credentials']);
                    return;
                }
                
                $payload = [
                    'user_id' => $user['id'],
                    'username' => $user['username'],
                    'role' => $user['role'],
                    'exp' => time() + JWT_EXPIRY
                ];
                
                $token = generateJWT($payload);
                
                echo json_encode([
                    'success' => true,
                    'token' => $token,
                    'user' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'role' => $user['role'],
                        'email' => $user['email']
                    ]
                ]);
                
                logActivity("User logged in via API: " . $username);
                
            } catch (PDOException $e) {
                logError("Login error: " . $e->getMessage());
                http_response_code(500);
                echo json_encode(['error' => 'Login failed']);
            }
            break;
            
        case 'register':
            $username = sanitizeInput($input['username'] ?? '');
            $password = $input['password'] ?? '';
            $email = sanitizeInput($input['email'] ?? '');
            $phone = sanitizeInput($input['phone'] ?? '');
            $orgCode = sanitizeInput($input['org_code'] ?? '');
            
            if (!$username || !$password || !$email) {
                http_response_code(400);
                echo json_encode(['error' => 'Username, password, and email required']);
                return;
            }
            
            if (!validateEmail($email)) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid email format']);
                return;
            }
            
            if ($phone && !validatePhone($phone)) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid phone number format']);
                return;
            }
            
            global $ORG_CODES;
            $role = 'user';
            if ($orgCode && isset($ORG_CODES[$orgCode])) {
                $role = $ORG_CODES[$orgCode];
            }
            
            try {
                $hashedPassword = hashPassword($password);
                
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, password, email, phone, role) 
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([$username, $hashedPassword, $email, $phone, $role]);
                
                $userId = $pdo->lastInsertId();
                
                echo json_encode([
                    'success' => true,
                    'message' => 'User registered successfully',
                    'user_id' => $userId
                ]);
                
                logActivity("New user registered via API: " . $username);
                
            } catch (PDOException $e) {
                if ($e->getCode() == 23000) { // Duplicate entry
                    http_response_code(409);
                    echo json_encode(['error' => 'Username or email already exists']);
                } else {
                    logError("Registration error: " . $e->getMessage());
                    http_response_code(500);
                    echo json_encode(['error' => 'Registration failed']);
                }
            }
            break;
            
        case 'refresh':
            $token = $input['token'] ?? '';
            $payload = verifyJWT($token);
            
            if (!$payload) {
                http_response_code(401);
                echo json_encode(['error' => 'Invalid token']);
                return;
            }
            
            $newPayload = [
                'user_id' => $payload['user_id'],
                'username' => $payload['username'],
                'role' => $payload['role'],
                'exp' => time() + JWT_EXPIRY
            ];
            
            $newToken = generateJWT($newPayload);
            
            echo json_encode([
                'success' => true,
                'token' => $newToken
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
}

// ============================================================================
// HEALTH CHECK
// ============================================================================

function handleHealthCheck() {
    global $pdo;
    
    $health = [
        'status' => 'healthy',
        'timestamp' => date('c'),
        'version' => APP_VERSION,
        'services' => []
    ];
    
    // Check database
    try {
        $pdo->query("SELECT 1");
        $health['services']['database'] = 'healthy';
    } catch (PDOException $e) {
        $health['services']['database'] = 'unhealthy';
        $health['status'] = 'degraded';
    }
    
    // Check file system
    $health['services']['filesystem'] = is_writable(UPLOAD_DIR) ? 'healthy' : 'unhealthy';
    
    // Check cache
    $health['services']['cache'] = is_writable(CACHE_DIR) ? 'healthy' : 'unhealthy';
    
    if (in_array('unhealthy', $health['services'])) {
        $health['status'] = 'unhealthy';
        http_response_code(503);
    }
    
    echo json_encode($health);
}
