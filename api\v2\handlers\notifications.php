<?php

function handleNotifications($method, $id, $user) {
    switch ($method) {
        case 'GET':
            if ($id) {
                getNotification($id, $user);
            } else {
                getNotifications($user);
            }
            break;
            
        case 'POST':
            createNotification($user);
            break;
            
        case 'PUT':
            if (!$id) {
                http_response_code(400);
                echo json_encode(['error' => 'Notification ID required']);
                return;
            }
            updateNotification($id, $user);
            break;
            
        case 'DELETE':
            if (!$id) {
                http_response_code(400);
                echo json_encode(['error' => 'Notification ID required']);
                return;
            }
            deleteNotification($id, $user);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
}

function getNotifications($user) {
    global $pdo;
    
    $page = intval($_GET['page'] ?? 1);
    $limit = min(intval($_GET['limit'] ?? 20), 100);
    $offset = ($page - 1) * $limit;
    
    $type = sanitizeInput($_GET['type'] ?? '');
    $unreadOnly = isset($_GET['unread_only']) && $_GET['unread_only'] === 'true';
    
    try {
        $whereConditions = ["user_id = ?"];
        $params = [$user['user_id']];
        
        if ($type) {
            $whereConditions[] = "type = ?";
            $params[] = $type;
        }
        
        if ($unreadOnly) {
            $whereConditions[] = "is_read = FALSE";
        }
        
        $whereClause = implode(" AND ", $whereConditions);
        
        $query = "
            SELECT id, message, type, is_read, created_at
            FROM notifications 
            WHERE $whereClause
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        ";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        $notifications = $stmt->fetchAll();
        
        // Get total count
        $countQuery = "SELECT COUNT(*) as total FROM notifications WHERE $whereClause";
        $countStmt = $pdo->prepare($countQuery);
        $countStmt->execute(array_slice($params, 0, -2));
        $totalCount = $countStmt->fetch()['total'];
        
        // Get unread count
        $unreadStmt = $pdo->prepare("SELECT COUNT(*) as unread FROM notifications WHERE user_id = ? AND is_read = FALSE");
        $unreadStmt->execute([$user['user_id']]);
        $unreadCount = $unreadStmt->fetch()['unread'];
        
        echo json_encode([
            'success' => true,
            'data' => $notifications,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => intval($totalCount),
                'pages' => ceil($totalCount / $limit)
            ],
            'unread_count' => intval($unreadCount)
        ]);
        
    } catch (PDOException $e) {
        logError("Failed to get notifications: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to retrieve notifications']);
    }
}

function getNotification($id, $user) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM notifications WHERE id = ? AND user_id = ?");
        $stmt->execute([$id, $user['user_id']]);
        $notification = $stmt->fetch();
        
        if (!$notification) {
            http_response_code(404);
            echo json_encode(['error' => 'Notification not found']);
            return;
        }
        
        echo json_encode([
            'success' => true,
            'data' => $notification
        ]);
        
    } catch (PDOException $e) {
        logError("Failed to get notification: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to retrieve notification']);
    }
}

function createNotification($user) {
    global $pdo;
    
    // Only admins and certain roles can create notifications
    if (!in_array($user['role'], ['admin', 'police', 'medical', 'fire_dept'])) {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    $message = sanitizeInput($input['message'] ?? '');
    $type = sanitizeInput($input['type'] ?? 'info');
    $targetUsers = $input['target_users'] ?? [];
    $broadcast = isset($input['broadcast']) && $input['broadcast'] === true;
    
    if (!$message) {
        http_response_code(400);
        echo json_encode(['error' => 'Message is required']);
        return;
    }
    
    try {
        $pdo->beginTransaction();
        
        if ($broadcast) {
            // Send to all users
            $stmt = $pdo->prepare("
                INSERT INTO notifications (user_id, message, type)
                SELECT id, ?, ? FROM users WHERE role != 'admin'
            ");
            $stmt->execute([$message, $type]);
            $affectedRows = $stmt->rowCount();
        } elseif (!empty($targetUsers)) {
            // Send to specific users
            $placeholders = str_repeat('?,', count($targetUsers) - 1) . '?';
            $stmt = $pdo->prepare("
                INSERT INTO notifications (user_id, message, type)
                SELECT id, ?, ? FROM users WHERE id IN ($placeholders)
            ");
            $params = array_merge([$message, $type], $targetUsers);
            $stmt->execute($params);
            $affectedRows = $stmt->rowCount();
        } else {
            http_response_code(400);
            echo json_encode(['error' => 'Target users or broadcast flag required']);
            return;
        }
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Notification sent successfully',
            'recipients' => $affectedRows
        ]);
        
        logActivity("Notification sent", 'INFO', [
            'sender_id' => $user['user_id'],
            'recipients' => $affectedRows,
            'type' => $type,
            'broadcast' => $broadcast
        ]);
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        logError("Failed to create notification: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to send notification']);
    }
}

function updateNotification($id, $user) {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Most common update is marking as read
    $isRead = isset($input['is_read']) ? (bool)$input['is_read'] : true;
    
    try {
        $stmt = $pdo->prepare("UPDATE notifications SET is_read = ? WHERE id = ? AND user_id = ?");
        $stmt->execute([$isRead, $id, $user['user_id']]);
        
        if ($stmt->rowCount() === 0) {
            http_response_code(404);
            echo json_encode(['error' => 'Notification not found']);
            return;
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Notification updated successfully'
        ]);
        
    } catch (PDOException $e) {
        logError("Failed to update notification: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update notification']);
    }
}

function deleteNotification($id, $user) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("DELETE FROM notifications WHERE id = ? AND user_id = ?");
        $stmt->execute([$id, $user['user_id']]);
        
        if ($stmt->rowCount() === 0) {
            http_response_code(404);
            echo json_encode(['error' => 'Notification not found']);
            return;
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Notification deleted successfully'
        ]);
        
    } catch (PDOException $e) {
        logError("Failed to delete notification: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete notification']);
    }
}

// Bulk operations
function markAllAsRead($user) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("UPDATE notifications SET is_read = TRUE WHERE user_id = ? AND is_read = FALSE");
        $stmt->execute([$user['user_id']]);
        
        echo json_encode([
            'success' => true,
            'message' => 'All notifications marked as read',
            'updated_count' => $stmt->rowCount()
        ]);
        
    } catch (PDOException $e) {
        logError("Failed to mark all notifications as read: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update notifications']);
    }
}

function deleteAllRead($user) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("DELETE FROM notifications WHERE user_id = ? AND is_read = TRUE");
        $stmt->execute([$user['user_id']]);
        
        echo json_encode([
            'success' => true,
            'message' => 'All read notifications deleted',
            'deleted_count' => $stmt->rowCount()
        ]);
        
    } catch (PDOException $e) {
        logError("Failed to delete read notifications: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete notifications']);
    }
}
?>
