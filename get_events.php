<?php
header('Content-Type: application/json');
require 'includes/db_connect.php';

$type = isset($_GET['type']) ? $_GET['type'] : null;

$query = "SELECT e.*, u.username FROM events e JOIN users u ON e.user_id = u.id";
$params = [];

if ($type) {
    $query .= " WHERE e.type = ?";
    $params[] = $type;
}

$stmt = $pdo->prepare($query);
$stmt->execute($params);

echo json_encode($stmt->fetchAll());
?>