# DOWN-RWANDA v2.0 - Advanced Emergency Response System

![DOWN-RWANDA Logo](assets/images/logo.png)

## 🚀 Overview

DOWN-RWANDA v2.0 is a comprehensive, advanced emergency response and event management system designed specifically for Rwanda. This enhanced version includes cutting-edge features like real-time analytics, AI-powered insights, mobile PWA support, and advanced emergency dispatch capabilities.

## ✨ New Advanced Features

### 🔥 Real-Time Features
- **Live Dashboard**: Real-time analytics with interactive charts and maps
- **WebSocket Integration**: Instant updates for critical events
- **Live Location Tracking**: Real-time police unit and user location tracking
- **Push Notifications**: Instant alerts for emergency situations

### 🤖 AI/ML Integration
- **Image Analysis**: Automatic severity assessment from uploaded images
- **Predictive Analytics**: Accident hotspot prediction and trend analysis
- **Smart Categorization**: Automatic event type classification
- **Response Time Prediction**: ML-based emergency response time estimation

### 📱 Progressive Web App (PWA)
- **Offline Functionality**: Works without internet connection
- **Mobile-First Design**: Optimized for mobile devices
- **App-Like Experience**: Install on home screen like native app
- **Background Sync**: Automatic data synchronization when online

### 📊 Advanced Analytics
- **Comprehensive Dashboards**: Multi-level analytics for different user roles
- **Performance Metrics**: System performance and response time analysis
- **Geographic Insights**: Heat maps and location-based analytics
- **Predictive Modeling**: Future trend predictions and resource planning

### 🔐 Enhanced Security
- **JWT Authentication**: Secure token-based authentication
- **API Rate Limiting**: Protection against abuse and DDoS
- **Role-Based Access Control**: Granular permissions system
- **Data Encryption**: End-to-end encryption for sensitive data

### 🌐 API-First Architecture
- **RESTful API v2**: Comprehensive API for all operations
- **API Documentation**: Auto-generated API documentation
- **Mobile SDK Ready**: Prepared for native mobile app development
- **Third-Party Integrations**: Easy integration with external systems

## 🏗️ System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile PWA    │    │   Web Dashboard │    │   Admin Panel   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      API Gateway v2       │
                    └─────────────┬─────────────┘
                                  │
          ┌───────────────────────┼───────────────────────┐
          │                       │                       │
    ┌─────┴─────┐         ┌───────┴───────┐       ┌───────┴───────┐
    │ Analytics │         │  Core System  │       │  AI/ML Engine │
    │  Engine   │         │   Services    │       │   Services    │
    └───────────┘         └───────┬───────┘       └───────────────┘
                                  │
                         ┌────────┴────────┐
                         │    Database     │
                         │   (Enhanced)    │
                         └─────────────────┘
```

## 🛠️ Installation & Setup

### Prerequisites
- PHP 8.0 or higher
- MySQL 8.0 or higher
- Apache/Nginx web server
- Composer (for dependency management)
- Node.js (for build tools)

### Quick Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/down-rwanda-v2.git
   cd down-rwanda-v2
   ```

2. **Install dependencies**
   ```bash
   composer install
   npm install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Database setup**
   ```bash
   # Create database
   mysql -u root -p -e "CREATE DATABASE down_rwanda;"
   
   # Run migrations (automatic on first access)
   php setup/migrate.php
   ```

5. **Build assets**
   ```bash
   npm run build
   ```

6. **Set permissions**
   ```bash
   chmod -R 755 uploads/ logs/ cache/
   ```

### Advanced Configuration

#### Environment Variables
Copy `.env.example` to `.env` and configure:

```env
# Database
DB_HOST=localhost
DB_DATABASE=down_rwanda
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Security
JWT_SECRET=your-super-secret-jwt-key
ENCRYPTION_KEY=your-32-character-encryption-key

# External APIs
GOOGLE_MAPS_API_KEY=your-google-maps-key
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token

# AI/ML Services
OPENAI_API_KEY=your-openai-key
GOOGLE_VISION_API_KEY=your-vision-key
```

#### Web Server Configuration

**Apache (.htaccess)**
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/v2/(.*)$ api/v2/index.php [QSA,L]
```

**Nginx**
```nginx
location /api/v2/ {
    try_files $uri $uri/ /api/v2/index.php?$query_string;
}

location /mobile/ {
    try_files $uri $uri/ /mobile/index.php?$query_string;
}
```

## 📱 Mobile PWA Features

### Installation
Users can install the PWA directly from their browser:
1. Visit the mobile site
2. Look for "Add to Home Screen" prompt
3. Follow installation instructions

### Offline Capabilities
- View cached events and data
- Report emergencies (queued for sync)
- Access user profile and settings
- View maps with cached tiles

### Push Notifications
```javascript
// Enable notifications
if ('Notification' in window) {
    Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
            // Subscribe to push notifications
            registerForPushNotifications();
        }
    });
}
```

## 🔌 API Documentation

### Authentication
```bash
# Login
POST /api/v2/auth
{
    "action": "login",
    "username": "<EMAIL>",
    "password": "password"
}

# Response
{
    "success": true,
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": { ... }
}
```

### Events API
```bash
# Get events
GET /api/v2/events?lat=-1.9403&lng=29.8739&radius=10

# Create event
POST /api/v2/events
Authorization: Bearer {token}
{
    "title": "Traffic Accident",
    "type": "accident",
    "latitude": -1.9403,
    "longitude": 29.8739,
    "severity_level": "high"
}
```

### Analytics API
```bash
# Get system analytics
GET /api/v2/analytics?range=30days
Authorization: Bearer {token}
```

## 🎯 User Roles & Permissions

### User Types
1. **Regular User**: Report events, view public information
2. **Organization**: Create paid events, access organization dashboard
3. **Police**: Access emergency dispatch, view all accidents
4. **Medical**: Access medical emergencies, ambulance dispatch
5. **Fire Department**: Access fire emergencies, fire truck dispatch
6. **Admin**: Full system access, analytics, user management

### Permission Matrix
| Feature | User | Org | Police | Medical | Fire | Admin |
|---------|------|-----|--------|---------|------|-------|
| Report Events | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| View Analytics | ❌ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Emergency Dispatch | ❌ | ❌ | ✅ | ✅ | ✅ | ✅ |
| User Management | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ |
| System Config | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ |

## 🔧 Advanced Features Configuration

### AI Image Analysis
```php
// Enable in config.php
define('AI_IMAGE_ANALYSIS', true);
define('AI_SEVERITY_DETECTION', true);

// Configure in .env
GOOGLE_VISION_API_KEY=your-api-key
OPENAI_API_KEY=your-openai-key
```

### Real-Time Updates
```javascript
// WebSocket connection
const socket = new WebSocket('ws://localhost:8080');
socket.onmessage = function(event) {
    const data = JSON.parse(event.data);
    if (data.type === 'new_emergency') {
        showEmergencyAlert(data.event);
    }
};
```

### Predictive Analytics
```php
// Get accident hotspots
$analytics = new AdvancedAnalytics($pdo);
$hotspots = $analytics->predictAccidentHotspots();

// Get resource recommendations
$recommendations = $analytics->getResourceRecommendations();
```

## 📊 Monitoring & Performance

### Health Checks
```bash
# System health
GET /api/v2/health

# Response
{
    "status": "healthy",
    "services": {
        "database": "healthy",
        "cache": "healthy",
        "filesystem": "healthy"
    }
}
```

### Performance Metrics
- Average API response time: < 200ms
- Database query optimization
- Caching for frequently accessed data
- CDN integration for static assets

## 🔒 Security Features

### Data Protection
- GDPR compliant data handling
- Automatic data anonymization
- Secure file upload validation
- SQL injection prevention
- XSS protection

### Access Control
- JWT token authentication
- Role-based permissions
- API rate limiting
- IP whitelisting for admin access

## 🌍 Internationalization

### Supported Languages
- English (en)
- Kinyarwanda (rw)
- French (fr)
- Swahili (sw)

### Adding New Languages
1. Create language file: `lang/{locale}.json`
2. Add to supported languages in config
3. Update frontend translation system

## 🚀 Deployment

### Production Deployment
```bash
# Build for production
npm run build:production

# Set production environment
cp .env.production .env

# Optimize autoloader
composer install --optimize-autoloader --no-dev

# Set proper permissions
chmod -R 755 storage/ bootstrap/cache/
```

### Docker Deployment
```dockerfile
FROM php:8.1-apache
COPY . /var/www/html/
RUN composer install --no-dev --optimize-autoloader
EXPOSE 80
```

## 🤝 Contributing

### Development Setup
```bash
# Install development dependencies
composer install
npm install

# Run development server
php -S localhost:8000

# Watch for changes
npm run watch
```

### Code Standards
- PSR-12 coding standards
- PHPUnit for testing
- ESLint for JavaScript
- Automated code formatting

## 📞 Support & Contact

- **Email**: <EMAIL>
- **Phone**: +250 788 123 456
- **Emergency**: +250 788 911 911
- **Documentation**: https://docs.downrwanda.com

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Rwanda National Police
- Ministry of Health Rwanda
- Rwanda Development Board
- Local communities and organizations

---

**DOWN-RWANDA v2.0** - Empowering communities through technology for safer Rwanda 🇷🇼
