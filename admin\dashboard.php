<?php
session_start();
require '../includes/db_connect.php';
require '../includes/auth_check.php';

// Admin only access
if ($_SESSION['role'] !== 'admin') {
    header("HTTP/1.1 403 Forbidden");
    exit;
}

// Get stats
$stats = $pdo->query("
    SELECT 
        (SELECT COUNT(*) FROM users) AS users,
        (SELECT COUNT(*) FROM events) AS events,
        (SELECT COUNT(*) FROM events WHERE is_paid = TRUE) AS paid_events,
        (SELECT COUNT(*) FROM external_events) AS external_events
")->fetch();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <span class="navbar-brand">Admin Portal</span>
            <div class="d-flex">
                <a href="approve_events.php" class="btn btn-sm btn-warning me-2">Approve Events</a>
                <a href="../logout.php" class="btn btn-sm btn-danger">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h2>System Overview</h2>
        
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card text-white bg-primary mb-3">
                    <div class="card-body">
                        <h1 class="card-title"><?= $stats['users'] ?></h1>
                        <p class="card-text">Total Users</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-success mb-3">
                    <div class="card-body">
                        <h1 class="card-title"><?= $stats['events'] ?></h1>
                        <p class="card-text">Total Events</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-info mb-3">
                    <div class="card-body">
                        <h1 class="card-title"><?= $stats['paid_events'] ?></h1>
                        <p class="card-text">Paid Events</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-secondary mb-3">
                    <div class="card-body">
                        <h1 class="card-title"><?= $stats['external_events'] ?></h1>
                        <p class="card-text">External Events</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Recent Events</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="eventsChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>User Types</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="usersChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <script>
        // Events by type chart
        new Chart(
            document.getElementById('eventsChart'),
            {
                type: 'bar',
                data: {
                    labels: ['Accidents', 'Shows', 'Parties'],
                    datasets: [{
                        label: 'Events by Type',
                        data: [
                            <?= $pdo->query("SELECT COUNT(*) FROM events WHERE type = 'accident'")->fetchColumn() ?>,
                            <?= $pdo->query("SELECT COUNT(*) FROM events WHERE type = 'show'")->fetchColumn() ?>,
                            <?= $pdo->query("SELECT COUNT(*) FROM events WHERE type = 'party'")->fetchColumn() ?>
                        ],
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)'
                        ]
                    }]
                }
            }
        );

        // User types pie chart
        new Chart(
            document.getElementById('usersChart'),
            {
                type: 'pie',
                data: {
                    labels: ['Regular Users', 'Organizations', 'Police'],
                    datasets: [{
                        data: [
                            <?= $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'user'")->fetchColumn() ?>,
                            <?= $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'organization'")->fetchColumn() ?>,
                            <?= $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'police'")->fetchColumn() ?>
                        ],
                        backgroundColor: [
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)'
                        ]
                    }]
                }
            }
        );
    </script>
</body>
</html>