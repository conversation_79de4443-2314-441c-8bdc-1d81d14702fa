# Environment configuration
.env
.env.local
.env.production

# Logs
logs/*.log
logs/*.txt
!logs/.gitkeep

# Cache files
cache/*.cache
cache/api/*.cache
cache/images/*
!cache/api/.gitkeep
!cache/images/.gitkeep

# Uploaded files
uploads/*
!uploads/.gitkeep
!uploads/events/.gitkeep
!uploads/avatars/.gitkeep
!uploads/evidence/.gitkeep
!uploads/payments/.gitkeep

# Temporary files
temp/*
!temp/.gitkeep

# Vendor dependencies
vendor/
node_modules/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.bak
*.backup
*.old

# Database dumps
*.sql
*.dump

# Compiled assets
/public/hot
/public/storage
/storage/*.key

# NPM/Yarn
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# PHP
composer.phar
/vendor/

# Laravel specific (if using Laravel components)
/bootstrap/compiled.php
/app/storage/

# Symfony specific (if using Symfony components)
/app/cache/*
/app/logs/*
/app/config/parameters.yml

# Test files
phpunit.xml
.phpunit.result.cache

# Coverage reports
/coverage/
clover.xml

# Build files
/build/
/dist/

# Documentation build
/docs/_build/

# Local development
.vagrant/
Vagrantfile.local

# Docker
docker-compose.override.yml
.dockerignore

# SSL certificates
*.pem
*.key
*.crt
*.csr

# Configuration files with sensitive data
config/database.php
config/mail.php
config/services.php

# Session files
sessions/*

# Error logs
error.log
access.log

# Maintenance mode
maintenance.flag

# Local scripts
local_*
dev_*
test_*

# Backup directories
backups/
dumps/

# Analytics and tracking
analytics/
tracking/

# Third-party integrations
integrations/local/

# Custom local files
local/
private/

# Archive files
*.zip
*.tar.gz
*.rar
*.7z
