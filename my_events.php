<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require 'includes/db_connect.php';
require 'includes/auth_check.php';
require 'includes/functions.php';
require 'config.php';

// Get filter parameters
$status = isset($_GET['status']) ? sanitizeInput($_GET['status']) : '';
$type = isset($_GET['type']) ? sanitizeInput($_GET['type']) : '';
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Build query for user's events
$whereConditions = ["e.user_id = ?"];
$params = [$_SESSION['user_id']];

if ($status === 'pending') {
    $whereConditions[] = "e.is_approved = FALSE";
} elseif ($status === 'approved') {
    $whereConditions[] = "e.is_approved = TRUE";
}

if ($type) {
    $whereConditions[] = "e.type = ?";
    $params[] = $type;
}

$whereClause = implode(' AND ', $whereConditions);

// Get user's events
$query = "
    SELECT e.*, ea.views, ea.shares,
           (SELECT COUNT(*) FROM event_images ei WHERE ei.event_id = e.id) as image_count
    FROM events e
    LEFT JOIN event_analytics ea ON e.id = ea.event_id
    WHERE $whereClause
    ORDER BY e.created_at DESC
    LIMIT $limit OFFSET $offset
";

$stmt = $pdo->prepare($query);
$stmt->execute($params);
$events = $stmt->fetchAll();

// Get total count for pagination
$countQuery = "SELECT COUNT(*) FROM events e WHERE $whereClause";
$countStmt = $pdo->prepare($countQuery);
$countStmt->execute($params);
$totalEvents = $countStmt->fetchColumn();
$totalPages = ceil($totalEvents / $limit);

// Get user statistics
$userStats = $pdo->prepare("
    SELECT
        COUNT(*) as total_events,
        COUNT(CASE WHEN is_approved = TRUE THEN 1 END) as approved_events,
        COUNT(CASE WHEN is_approved = FALSE THEN 1 END) as pending_events,
        COUNT(CASE WHEN severity_level = 'critical' THEN 1 END) as critical_events,
        COALESCE(SUM(ea.views), 0) as total_views
    FROM events e
    LEFT JOIN event_analytics ea ON e.id = ea.event_id
    WHERE e.user_id = ?
");
$userStats->execute([$_SESSION['user_id']]);
$stats = $userStats->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Events | <?= APP_NAME ?></title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 5px 20px rgba(0, 0, 0, 0.15);
            --border-radius: 15px;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            box-shadow: var(--shadow-light);
        }

        .navbar-brand {
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .main-content {
            padding: 2rem 0;
        }

        .page-header {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-light);
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .stat-card {
            background: rgba(52, 152, 219, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-light);
        }

        .stat-card.approved {
            background: rgba(39, 174, 96, 0.1);
            border-color: var(--success-color);
        }

        .stat-card.pending {
            background: rgba(243, 156, 18, 0.1);
            border-color: var(--warning-color);
        }

        .stat-card.critical {
            background: rgba(231, 76, 60, 0.1);
            border-color: var(--danger-color);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }

        .filters-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-light);
        }

        .filter-chips {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-chip {
            background: #e9ecef;
            border: none;
            border-radius: 20px;
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #6c757d;
        }

        .filter-chip:hover {
            background: var(--secondary-color);
            color: white;
            text-decoration: none;
        }

        .filter-chip.active {
            background: var(--primary-color);
            color: white;
        }

        .events-container {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
            overflow: hidden;
        }

        .events-header {
            background: #f8f9fa;
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
        }

        .event-item {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .event-item:hover {
            background: #f8f9fa;
        }

        .event-item:last-child {
            border-bottom: none;
        }

        .event-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .event-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .event-meta {
            display: flex;
            gap: 1rem;
            font-size: 0.85rem;
            color: #6c757d;
            flex-wrap: wrap;
        }

        .event-status {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-approved {
            background: rgba(39, 174, 96, 0.1);
            color: var(--success-color);
        }

        .status-pending {
            background: rgba(243, 156, 18, 0.1);
            color: var(--warning-color);
        }

        .type-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            background: rgba(52, 152, 219, 0.1);
            color: var(--secondary-color);
        }

        .severity-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .severity-low { background: rgba(39, 174, 96, 0.1); color: var(--success-color); }
        .severity-medium { background: rgba(52, 152, 219, 0.1); color: var(--secondary-color); }
        .severity-high { background: rgba(243, 156, 18, 0.1); color: var(--warning-color); }
        .severity-critical { background: rgba(231, 76, 60, 0.1); color: var(--danger-color); }

        .event-description {
            color: #6c757d;
            line-height: 1.5;
            margin-bottom: 1rem;
        }

        .event-stats {
            display: flex;
            gap: 1rem;
            font-size: 0.85rem;
            color: #6c757d;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.8rem;
            border-radius: 6px;
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
        }

        .empty-state i {
            font-size: 4rem;
            color: #dee2e6;
            margin-bottom: 1rem;
        }

        .pagination-wrapper {
            display: flex;
            justify-content: center;
            margin-top: 2rem;
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 1rem 0;
            }

            .page-header {
                padding: 1.5rem;
            }

            .stats-row {
                grid-template-columns: repeat(2, 1fr);
                gap: 0.75rem;
            }

            .stat-card {
                padding: 1rem;
            }

            .event-header {
                flex-direction: column;
                gap: 1rem;
            }

            .event-meta {
                flex-direction: column;
                gap: 0.5rem;
            }

            .action-buttons {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-shield-alt me-2"></i>
                DOWN-RWANDA
            </a>

            <div class="d-flex gap-2">
                <a href="report_event.php" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus me-1"></i>
                    Report Event
                </a>
                <a href="index.php" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-arrow-left me-1"></i>
                    Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-user-circle me-3"></i>
                My Events
            </h1>
            <p class="text-muted mb-0">Manage and track your reported events</p>

            <div class="stats-row">
                <div class="stat-card">
                    <div class="stat-number"><?= number_format($stats['total_events']) ?></div>
                    <div class="stat-label">Total Events</div>
                </div>
                <div class="stat-card approved">
                    <div class="stat-number"><?= number_format($stats['approved_events']) ?></div>
                    <div class="stat-label">Approved</div>
                </div>
                <div class="stat-card pending">
                    <div class="stat-number"><?= number_format($stats['pending_events']) ?></div>
                    <div class="stat-label">Pending</div>
                </div>
                <div class="stat-card critical">
                    <div class="stat-number"><?= number_format($stats['critical_events']) ?></div>
                    <div class="stat-label">Critical</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= number_format($stats['total_views']) ?></div>
                    <div class="stat-label">Total Views</div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters-card">
            <h5 class="mb-3">
                <i class="fas fa-filter me-2"></i>
                Filter My Events
            </h5>

            <!-- Status Filters -->
            <div class="mb-3">
                <label class="form-label">Status:</label>
                <div class="filter-chips">
                    <a href="my_events.php<?= $type ? "?type=$type" : '' ?>"
                       class="filter-chip <?= empty($status) ? 'active' : '' ?>">All</a>
                    <a href="?status=approved<?= $type ? "&type=$type" : '' ?>"
                       class="filter-chip <?= $status === 'approved' ? 'active' : '' ?>">Approved</a>
                    <a href="?status=pending<?= $type ? "&type=$type" : '' ?>"
                       class="filter-chip <?= $status === 'pending' ? 'active' : '' ?>">Pending</a>
                </div>
            </div>

            <!-- Type Filters -->
            <div>
                <label class="form-label">Type:</label>
                <div class="filter-chips">
                    <a href="my_events.php<?= $status ? "?status=$status" : '' ?>"
                       class="filter-chip <?= empty($type) ? 'active' : '' ?>">All Types</a>
                    <?php foreach ($EVENT_TYPES as $eventType => $config): ?>
                    <a href="?type=<?= $eventType ?><?= $status ? "&status=$status" : '' ?>"
                       class="filter-chip <?= $type === $eventType ? 'active' : '' ?>">
                        <?= $config['name'] ?>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Events List -->
        <div class="events-container">
            <div class="events-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Your Events (<?= number_format($totalEvents) ?>)
                </h5>
            </div>

            <?php if (empty($events)): ?>
                <div class="empty-state">
                    <i class="fas fa-calendar-times"></i>
                    <h4>No Events Found</h4>
                    <p class="text-muted mb-4">
                        <?php if ($status || $type): ?>
                            No events match your current filters.
                        <?php else: ?>
                            You haven't reported any events yet.
                        <?php endif; ?>
                    </p>
                    <?php if ($status || $type): ?>
                        <a href="my_events.php" class="btn btn-primary">Clear Filters</a>
                    <?php else: ?>
                        <a href="report_event.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Report Your First Event
                        </a>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <?php foreach ($events as $event): ?>
                <div class="event-item" onclick="viewEvent(<?= $event['id'] ?>)">
                    <div class="event-header">
                        <div>
                            <div class="event-title"><?= htmlspecialchars($event['title']) ?></div>
                            <div class="event-meta">
                                <span><i class="fas fa-calendar me-1"></i><?= formatTimeAgo($event['created_at']) ?></span>
                                <span><i class="fas fa-map-marker-alt me-1"></i><?= htmlspecialchars($event['location_description'] ?: 'Location not specified') ?></span>
                                <?php if ($event['views']): ?>
                                <span><i class="fas fa-eye me-1"></i><?= $event['views'] ?> views</span>
                                <?php endif; ?>
                                <?php if ($event['image_count']): ?>
                                <span><i class="fas fa-images me-1"></i><?= $event['image_count'] ?> images</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="event-status">
                            <span class="status-badge status-<?= $event['is_approved'] ? 'approved' : 'pending' ?>">
                                <?= $event['is_approved'] ? 'Approved' : 'Pending' ?>
                            </span>
                            <span class="type-badge"><?= ucfirst($event['type']) ?></span>
                            <span class="severity-badge severity-<?= $event['severity_level'] ?>">
                                <?= ucfirst($event['severity_level']) ?>
                            </span>
                        </div>
                    </div>

                    <?php if ($event['description']): ?>
                    <div class="event-description">
                        <?= htmlspecialchars(substr($event['description'], 0, 200)) ?>
                        <?= strlen($event['description']) > 200 ? '...' : '' ?>
                    </div>
                    <?php endif; ?>

                    <div class="action-buttons">
                        <button class="btn btn-outline-primary btn-sm" onclick="event.stopPropagation(); viewEvent(<?= $event['id'] ?>)">
                            <i class="fas fa-eye me-1"></i>View Details
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="event.stopPropagation(); viewOnMap(<?= $event['latitude'] ?>, <?= $event['longitude'] ?>)">
                            <i class="fas fa-map me-1"></i>View on Map
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="event.stopPropagation(); shareEvent(<?= $event['id'] ?>)">
                            <i class="fas fa-share me-1"></i>Share
                        </button>
                        <?php if (!$event['is_approved']): ?>
                        <button class="btn btn-outline-warning btn-sm" onclick="event.stopPropagation(); editEvent(<?= $event['id'] ?>)">
                            <i class="fas fa-edit me-1"></i>Edit
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
        <div class="pagination-wrapper">
            <nav>
                <ul class="pagination">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $page - 1 ?><?= $status ? "&status=$status" : '' ?><?= $type ? "&type=$type" : '' ?>">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                    <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                        <a class="page-link" href="?page=<?= $i ?><?= $status ? "&status=$status" : '' ?><?= $type ? "&type=$type" : '' ?>">
                            <?= $i ?>
                        </a>
                    </li>
                    <?php endfor; ?>

                    <?php if ($page < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $page + 1 ?><?= $status ? "&status=$status" : '' ?><?= $type ? "&type=$type" : '' ?>">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewEvent(eventId) {
            window.location.href = `event.php?id=${eventId}`;
        }

        function viewOnMap(lat, lng) {
            window.open(`map.php?lat=${lat}&lng=${lng}&zoom=16`, '_blank');
        }

        function shareEvent(eventId) {
            const url = `${window.location.origin}/event.php?id=${eventId}`;

            if (navigator.share) {
                navigator.share({
                    title: 'Event Report',
                    url: url
                });
            } else {
                navigator.clipboard.writeText(url).then(() => {
                    alert('Event link copied to clipboard!');
                });
            }
        }

        function editEvent(eventId) {
            window.location.href = `edit_event.php?id=${eventId}`;
        }

        // Auto-refresh every 5 minutes to check for approval status updates
        setTimeout(() => {
            location.reload();
        }, 300000);
    </script>
</body>
</html>